<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="智能照片分析器 - 分析EXIF数据，发现拍摄规律">
    <meta name="theme-color" content="#4568dc">
    <title>{% block title %}照片分析器{% endblock %}</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="https://cdn.plot.ly/plotly-latest.min.js" as="script">

    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="/static/css/modern-framework.css">

    <!-- External dependencies -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js" defer></script>

    <!-- Favicon and app icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📸</text></svg>">

    {% block head %}{% endblock %}
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">跳转到主要内容</a>

    <!-- Modern responsive navigation -->
    <nav class="navbar" role="navigation" aria-label="主导航">
        <div class="nav-container">
            <a href="/" class="nav-brand" aria-label="照片分析器首页">
                <span class="brand-icon" aria-hidden="true">📸</span>
                <span class="brand-text">照片分析器</span>
            </a>

            <!-- Mobile menu toggle -->
            <button class="nav-toggle" aria-label="切换导航菜单" aria-expanded="false">
                <span class="hamburger"></span>
                <span class="hamburger"></span>
                <span class="hamburger"></span>
            </button>

            <!-- Navigation menu -->
            <ul class="nav-menu" role="menubar">
                <li role="none">
                    <a href="/" class="nav-link {% if request.url.path == '/' %}active{% endif %}"
                       role="menuitem" aria-current="{% if request.url.path == '/' %}page{% endif %}">
                        <span class="nav-icon" aria-hidden="true">🏠</span>
                        <span class="nav-text">主页</span>
                    </a>
                </li>
                <li role="none">
                    <a href="/data" class="nav-link {% if request.url.path == '/data' %}active{% endif %}"
                       role="menuitem" aria-current="{% if request.url.path == '/data' %}page{% endif %}">
                        <span class="nav-icon" aria-hidden="true">📊</span>
                        <span class="nav-text">数据</span>
                    </a>
                </li>
                <li role="none">
                    <a href="/devices" class="nav-link {% if request.url.path == '/devices' %}active{% endif %}"
                       role="menuitem" aria-current="{% if request.url.path == '/devices' %}page{% endif %}">
                        <span class="nav-icon" aria-hidden="true">📱</span>
                        <span class="nav-text">设备</span>
                    </a>
                </li>
                <li role="none">
                    <a href="/settings" class="nav-link {% if request.url.path == '/settings' %}active{% endif %}"
                       role="menuitem" aria-current="{% if request.url.path == '/settings' %}page{% endif %}">
                        <span class="nav-icon" aria-hidden="true">⚙️</span>
                        <span class="nav-text">设置</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main content area -->
    <main id="main-content" class="main-content" role="main">
        {% block content %}{% endblock %}
    </main>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="loading-indicator" aria-hidden="true">
        <div class="loading-spinner"></div>
        <span class="loading-text">加载中...</span>
    </div>

    <!-- Toast notifications -->
    <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>

    <!-- Modern JavaScript -->
    <script src="/static/js/modern-app.js" defer></script>
    {% block scripts %}{% endblock %}
</body>
</html>