<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}照片分析器{% endblock %}</title>
    <!-- 核心样式文件 -->
    <link rel="stylesheet" href="/static/css/chart-analysis.css">
    <link rel="stylesheet" href="/static/css/structured-analysis.css">
    <!-- 新的美观界面样式 -->
    <link rel="stylesheet" href="/static/css/modern-beautiful.css">
    <!-- 增强美化样式 -->
    <link rel="stylesheet" href="/static/css/enhanced-modern.css">
    <link rel="stylesheet" href="/static/css/no-animations.css">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="/static/js/disable-animations.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 现代化导航栏 -->
    <nav class="navbar-modern">
        <div class="nav-container-modern">
            <a href="/" class="logo-modern">
                <span class="logo-icon">📸</span>
                <span>照片分析器</span>
            </a>
            <ul class="nav-links-modern">
                <li><a href="/" class="{% if request.url.path == '/' %}active{% endif %}">
                    <span class="nav-icon">🏠</span>主页
                </a></li>
                <li><a href="/data" class="{% if request.url.path == '/data' %}active{% endif %}">
                    <span class="nav-icon">📊</span>数据
                </a></li>
                <li><a href="/devices" class="{% if request.url.path == '/devices' %}active{% endif %}">
                    <span class="nav-icon">📱</span>设备
                </a></li>
                <li><a href="/settings" class="{% if request.url.path == '/settings' %}active{% endif %}">
                    <span class="nav-icon">⚙️</span>设置
                </a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container">
        {% block content %}{% endblock %}
    </div>

    <!-- 脚本 -->
    <script src="/static/js/optimized-app.js"></script>
    <script src="/static/js/chart-analysis.js"></script>
    <script src="/static/js/minimal-interface.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>