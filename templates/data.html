{% extends "base.html" %}

{% block title %}数据统计 | 照片分析器{% endblock %}

{% block content %}
<div class="card">
    <h1 class="card-title">数据统计面板</h1>
    <p style="text-align: center; color: #64748b;">详细的拍摄参数统计数据</p>
</div>

<!-- 数据图表 -->
<div class="charts-section">
    <div class="charts-grid">
        <!-- 焦距统计 -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>📏 焦距使用统计</h3>
                <p class="chart-subtitle">按焦距范围统计使用频率</p>
            </div>
            <div id="focal-lengths-chart" class="chart-content"></div>
            <div id="focal_lengths-analysis" class="analysis-card" style="display: none;">
                <h4><span>✨ 数据洞察</span></h4>
                <p class="analysis-text"></p>
            </div>
        </div>

        <!-- 光圈统计 -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>🔆 光圈使用统计</h3>
                <p class="chart-subtitle">按光圈值统计使用频率</p>
            </div>
            <div id="apertures-chart" class="chart-content"></div>
            <div id="apertures-analysis" class="analysis-card" style="display: none;">
                <h4><span>✨ 数据洞察</span></h4>
                <p class="analysis-text"></p>
            </div>
        </div>

        <!-- 快门速度统计 -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>⚡ 快门速度统计</h3>
                <p class="chart-subtitle">按快门速度统计使用频率</p>
            </div>
            <div id="shutter-speeds-chart" class="chart-content"></div>
            <div id="shutter_speeds-analysis" class="analysis-card" style="display: none;">
                <h4><span>✨ 数据洞察</span></h4>
                <p class="analysis-text"></p>
            </div>
        </div>

        <!-- ISO统计 -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>📈 ISO使用统计</h3>
                <p class="chart-subtitle">按ISO值统计使用频率</p>
            </div>
            <div id="iso-values-chart" class="chart-content"></div>
            <div id="iso_values-analysis" class="analysis-card" style="display: none;">
                <h4><span>✨ 数据洞察</span></h4>
                <p class="analysis-text"></p>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="actions-container">
    <button onclick="analyzePhotos()" class="btn btn-primary">🔄 重新分析</button>
    <a href="/" class="btn btn-secondary">🏠 返回主页</a>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载图表数据
    setTimeout(loadDataCharts, 100);
    
    // 加载已保存的分析结果
    setTimeout(loadSavedAnalysisResults, 300);
    
    // 检查LLM状态并自动分析图表
    setTimeout(checkLlmStatusAndAnalyze, 500);
});
</script>
{% endblock %}
