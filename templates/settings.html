{% extends "base.html" %}

{% block title %}设置 | 照片分析器{% endblock %}

{% block content %}
<div class="card">
    <h1 class="card-title">设置</h1>
    <p style="text-align: center; color: #64748b;">管理照片文件夹路径和应用设置</p>
</div>

<!-- 主设置菜单 -->
<div id="main-settings" class="settings-menu">
    <div class="settings-grid">
        <div class="settings-card" onclick="showSettingsPage('file-management')">
            <div class="settings-icon">📁</div>
            <h3>文件管理</h3>
            <p>管理照片文件夹和路径设置</p>
        </div>
        <div class="settings-card" onclick="showSettingsPage('llm-settings')">
            <div class="settings-icon">🤖</div>
            <h3>LLM设置</h3>
            <p>配置AI分析服务的地址和参数</p>
        </div>
        <div class="settings-card" onclick="showSettingsPage('system-info')">
            <div class="settings-icon">ℹ️</div>
            <h3>系统信息</h3>
            <p>查看系统状态和版本信息</p>
        </div>
        <div class="settings-card" onclick="showSettingsPage('system-operations')">
            <div class="settings-icon">🔧</div>
            <h3>系统操作</h3>
            <p>维护操作和系统管理</p>
        </div>
    </div>
</div>

<!-- 文件管理页面 -->
<div id="file-management" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2>文件管理</h2>
        </div>
    </div>

    <!-- 添加新路径 -->
    <div class="card">
        <h3>添加照片文件夹</h3>
        <div class="form-group">
            <label for="new-path" class="form-label">文件夹路径</label>
            <input type="text" id="new-path" class="form-input" placeholder="请输入照片文件夹的完整路径，例如：C:\Photos">
            <small class="form-help">提示：可以直接输入路径，或点击"浏览文件夹"按钮选择</small>
        </div>
        
        <div id="current-selection" style="margin: 1rem 0;"></div>
        
        <div class="form-actions">
            <button onclick="addPath()" class="btn btn-primary">➕ 添加路径</button>
            <button onclick="selectFolder()" class="btn btn-secondary">📂 浏览文件夹</button>
            <button onclick="clearPath()" class="btn btn-outline">🗑️ 清空</button>
        </div>
    </div>

    <!-- 已添加的路径列表 -->
    <div class="card">
        <h3>已添加的照片路径</h3>
        
        {% if paths %}
        <div class="path-list">
            {% for path in paths %}
            <div class="path-item">
                <span class="path-text">{{ path }}</span>
                <button data-path="{{ path }}" onclick="removePath(this.dataset.path)" class="btn btn-danger">🗑️ 删除</button>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">📂</div>
            <h4 class="empty-state-title">暂无照片路径</h4>
            <p class="empty-state-description">请添加包含照片的文件夹路径开始分析</p>
        </div>
        {% endif %}
    </div>
    
    <!-- 照片管理操作 -->
    <div class="card">
        <h3>照片管理操作</h3>
        <div class="form-actions">
            <button onclick="analyzePhotos()" class="btn btn-primary">🔍 重新分析所有照片</button>
            <button onclick="clearAllPaths()" class="btn btn-danger">🗑️ 清空所有路径</button>
        </div>
        
        <div class="alert alert-info" style="margin-top: 1rem;">
            <strong>重新分析：</strong> 点击此按钮将重新扫描所有已添加路径中的照片并更新统计数据。
        </div>
    </div>
</div>

<!-- 系统信息页面 -->
<div id="system-info" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2>系统信息</h2>
        </div>
    </div>
    
    <div class="card">
        <h3>系统状态</h3>
        <div class="system-info-grid">
            <div class="info-item">
                <div class="info-icon">🚀</div>
                <div class="info-content">
                    <h4>应用版本</h4>
                    <p>1.0.0</p>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon">🖼️</div>
                <div class="info-content">
                    <h4>支持格式</h4>
                    <p>JPEG, PNG, TIFF</p>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon">⚙️</div>
                <div class="info-content">
                    <h4>配置文件</h4>
                    <p>config.json</p>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon">💾</div>
                <div class="info-content">
                    <h4>数据目录</h4>
                    <p>./data</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3>运行统计</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ paths|length if paths else 0 }}</div>
                <div class="stat-label">已添加路径</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_photos if total_photos else 0 }}</div>
                <div class="stat-label">已分析照片</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ data_size if data_size else '-' }}</div>
                <div class="stat-label">存储空间</div>
            </div>
        </div>
    </div>
</div>

<!-- 系统操作页面 -->
<div id="system-operations" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2>系统操作</h2>
        </div>
    </div>
    
    <div class="card">
        <h3>数据管理</h3>
        <div class="operation-grid">
            <div class="operation-card">
                <div class="operation-icon">📤</div>
                <h4>导出设置</h4>
                <p>导出当前配置和路径设置</p>
                <button onclick="exportSettings()" class="btn btn-primary">导出</button>
            </div>
            <div class="operation-card">
                <div class="operation-icon">📥</div>
                <h4>导入设置</h4>
                <p>从文件导入配置和路径设置</p>
                <button onclick="importSettings()" class="btn btn-secondary">导入</button>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3>内容分析</h3>
        <p class="form-help">管理AI内容分析功能，对照片进行智能评分和分类</p>
        <div class="form-actions">
            <button onclick="triggerContentAnalysis()" class="btn btn-primary">🤖 启动内容分析</button>
            <button onclick="checkAnalysisStatus()" class="btn btn-secondary">📊 检查分析状态</button>
            <button onclick="generateAnalysisReport()" class="btn btn-outline">📋 生成分析报告</button>
        </div>
        <div id="content-analysis-status" class="alert" style="display: none; margin-top: 1rem;"></div>
        <div class="alert alert-info" style="margin-top: 1rem;">
            <strong>📝 说明：</strong> 内容分析使用AI对照片进行质量评分、色调分析和题材分类，需要LLM服务支持。
        </div>
    </div>
    
    <div class="card">
        <h3>系统维护</h3>
        <div class="form-actions">
            <button onclick="refreshSystem()" class="btn btn-secondary">🔄 刷新系统</button>
            <button onclick="clearCache()" class="btn btn-outline">🗑️ 清除缓存</button>
            <button onclick="resetSettings()" class="btn btn-danger">⚠️ 重置设置</button>
        </div>
        <div class="alert alert-warning" style="margin-top: 1rem;">
            <strong>⚠️ 注意：</strong> 重置设置将清空所有配置，请谨慎操作！
        </div>
    </div>
    
    <div class="card">
        <h3>快捷操作</h3>
        <div class="form-actions">
            <a href="/" class="btn btn-primary">🏠 返回主页</a>
            <a href="/data" class="btn btn-secondary">📊 查看数据</a>
            <a href="/devices" class="btn btn-outline">📱 设备信息</a>
        </div>
    </div>
</div>

<!-- LLM设置页面 -->
<div id="llm-settings" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2>LLM设置</h2>
        </div>
    </div>
    
    <div class="card">
        <h3>AI分析服务配置</h3>
        <p class="form-help">配置本地或远程大语言模型服务，用于分析照片数据</p>
        
        <div class="form-group">
            <label for="llm-api-url" class="form-label">API地址</label>
            <input type="text" id="llm-api-url" class="form-input" value="{{ llm_config.api_url }}" placeholder="http://localhost:11434">
            <small class="form-help">输入LLM服务的地址，例如：http://localhost:11434</small>
        </div>
        
        <div class="form-group">
            <label for="llm-model" class="form-label">模型名称</label>
            <input type="text" id="llm-model" class="form-input" value="{{ llm_config.model }}" placeholder="gemma3:4b">
            <small class="form-help">输入要使用的模型名称，例如：gemma3:4b</small>
        </div>
        
        <div class="form-group">
            <label class="form-label">启用AI分析</label>
            <div class="toggle-switch">
                <input type="checkbox" id="llm-enabled" {% if llm_config.enabled %}checked{% endif %}>
                <label for="llm-enabled"></label>
            </div>
            <small class="form-help">开启后，将在图表下方自动显示数据洞察</small>
        </div>
        
        <div class="form-actions">
            <button onclick="saveLlmSettings()" class="btn btn-primary">💾 保存设置</button>
            <button onclick="testLlmConnection()" class="btn btn-secondary">🔄 测试连接</button>
            <button onclick="clearAndReanalyze()" class="btn btn-outline">🔍 重新分析所有数据</button>
        </div>
        
        <div id="llm-test-result" class="alert" style="display: none; margin-top: 1rem;"></div>
    </div>
    
    <div class="card">
        <h3>AI分析管理</h3>
        <p class="form-help">管理已保存的分析结果</p>
        
        <div class="form-actions">
            <button onclick="clearAnalysisResults()" class="btn btn-danger">🗑️ 清除所有分析结果</button>
        </div>
        
        <div class="alert alert-info" style="margin-top: 1rem;">
            <strong>提示：</strong> 清除分析结果后，系统将在需要时重新生成分析。已有数据不会被删除。
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/settings.js"></script>
{% endblock %}
