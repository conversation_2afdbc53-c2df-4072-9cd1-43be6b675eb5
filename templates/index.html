{% extends "base.html" %}

{% block title %}主页 | 照片分析器{% endblock %}

{% block content %}
<!-- 现代化主页布局 -->
<div class="modern-dashboard">
    <!-- 头部标题区域 -->
    <div class="dashboard-header">
        <div class="header-content">
            <h1 class="dashboard-title">
                <span class="title-icon">📸</span>
                <span class="title-text">照片分析仪表板</span>
            </h1>
            <p class="dashboard-subtitle">智能分析您的摄影作品，发现拍摄规律与精彩瞬间</p>
        </div>
    </div>

    {% if not has_data %}
    <!-- 空状态展示 -->
    <div class="empty-dashboard">
        <div class="empty-content">
            <div class="empty-icon">📷</div>
            <h2 class="empty-title">开始您的摄影分析之旅</h2>
            <p class="empty-description">
                {% if config.paths|length > 0 %}
                    已配置 {{ config.paths|length }} 个监控路径，点击下方按钮开始分析照片数据。
                {% else %}
                    请先在设置页面添加包含照片的文件夹路径，我们将自动分析EXIF信息并生成详细的统计报告。
                {% endif %}
            </p>
            <div class="empty-actions">
                {% if config.paths|length > 0 %}
                    <button onclick="analyzePhotos()" class="btn-primary-large">
                        <span class="btn-icon">🔍</span>
                        <span class="btn-text">开始分析照片</span>
                    </button>
                {% endif %}
                <a href="/settings" class="btn-secondary-large">
                    <span class="btn-icon">⚙️</span>
                    <span class="btn-text">管理设置</span>
                </a>
            </div>
        </div>
    </div>
    {% else %}
    
    <!-- 统计概览卡片 -->
    <div class="stats-overview">
        <div class="stat-card-modern">
            <div class="stat-icon">📸</div>
            <div class="stat-info">
                <div class="stat-number">{{ total_photos }}</div>
                <div class="stat-label">照片总数</div>
            </div>
        </div>
        
        {% if analysis_report %}
        <div class="stat-card-modern">
            <div class="stat-icon">⭐</div>
            <div class="stat-info">
                <div class="stat-number">{{ "%.1f"|format(analysis_report.平均评分) }}</div>
                <div class="stat-label">平均评分</div>
            </div>
        </div>
        <div class="stat-card-modern">
            <div class="stat-icon">🏆</div>
            <div class="stat-info">
                <div class="stat-number">{{ analysis_report.最高评分 }}</div>
                <div class="stat-label">最高评分</div>
            </div>
        </div>
        <div class="stat-card-modern">
            <div class="stat-icon">📊</div>
            <div class="stat-info">
                <div class="stat-number">{{ analysis_report.最低评分 }}</div>
                <div class="stat-label">最低评分</div>
            </div>
        </div>
        {% endif %}
        
        {% if content_analysis_status and content_analysis_status.total > 0 %}
        <div class="stat-card-modern">
            <div class="stat-icon">✅</div>
            <div class="stat-info">
                <div class="stat-number">{{ content_analysis_status.analyzed }}</div>
                <div class="stat-label">已分析</div>
            </div>
        </div>
        <div class="stat-card-modern">
            <div class="stat-icon">⏳</div>
            <div class="stat-info">
                <div class="stat-number">{{ content_analysis_status.unanalyzed }}</div>
                <div class="stat-label">待分析</div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
        <!-- 左侧：最佳照片展示 -->
        <div class="content-left">
            {% if analysis_report and analysis_report.评分最佳照片 %}
            <div class="best-photos-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <span class="section-icon">🏆</span>
                        最佳照片展示
                    </h2>
                    <p class="section-subtitle">AI评分最高的精选作品</p>
                </div>
                <div class="best-photos-container">
                    {% for photo in analysis_report.评分最佳照片[:3] %}
                    <div class="photo-card">
                        <div class="photo-image">
                            <img 
                                src="/api/photo-thumbnail/{{ photo.路径 | urlencode }}?width=300&height=200" 
                                alt="{{ photo.文件名 }}"
                                loading="lazy"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                            >
                            <div class="photo-placeholder" style="display: none;">
                                <div class="placeholder-icon">📷</div>
                                <div class="placeholder-text">无法加载</div>
                            </div>
                            <div class="photo-score">{{ photo.评分 }}</div>
                        </div>
                        <div class="photo-details">
                            <h3 class="photo-title">{{ photo.文件名 }}</h3>
                            <div class="photo-meta">
                                <div class="meta-item">
                                    <span class="meta-icon">📷</span>
                                    <span class="meta-text">{{ photo.拍摄设备 or '未知设备' }}</span>
                                </div>
                                <div class="meta-item">
                                    <span class="meta-icon">�</span>
                                    <span class="meta-text">{{ photo.镜头 or '未知镜头' }}</span>
                                </div>
                                <div class="meta-item">
                                    <span class="meta-icon">⚙️</span>
                                    <span class="meta-text">{{ photo.光圈 }} · {{ photo.快门 }} · ISO{{ photo.ISO }}</span>
                                </div>
                            </div>
                            <button 
                                class="view-photo-btn" 
                                onclick="viewFullPhoto('{{ photo.路径 | urlencode }}', '{{ photo.文件名 }}')"
                            >
                                查看原图
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- 右侧：统计信息 -->
        <div class="content-right">
            <!-- 设备统计 -->
            <div class="info-panel">
                <div class="panel-header">
                    <h3 class="panel-title">
                        <span class="panel-icon">📱</span>
                        设备统计
                    </h3>
                </div>
                <div class="panel-content">
                    <div class="info-item">
                    </div>
                    <div class="info-item">
                        <span class="info-label">最常用设备</span>
                        <span class="info-value" id="top-device">加载中...</span>
                        <span class="info-count" id="top-device-count"></span>
                    </div>
                </div>
            </div>

            <!-- 镜头统计 -->
            <div class="info-panel">
                <div class="panel-header">
                    <h3 class="panel-title">
                        <span class="panel-icon">🔍</span>
                        镜头统计
                    </h3>
                </div>
                <div class="panel-content">
                    <div class="info-item">
                    </div>
                    <div class="info-item">
                        <span class="info-label">最常用镜头</span>
                        <span class="info-value" id="top-lens">加载中...</span>
                        <span class="info-count" id="top-lens-count"></span>
                    </div>
                </div>
            </div>

            <!-- 拍摄偏好 -->
            <div class="info-panel">
                <div class="panel-header">
                    <h3 class="panel-title">
                        <span class="panel-icon">⭐</span>
                        拍摄偏好
                    </h3>
                </div>
                <div class="panel-content">
                    <div class="info-item">
                        <span class="info-label">常用搭配</span>
                        <span class="info-value" id="device-lens-combo">加载中...</span>
                        <span class="info-count" id="device-lens-combo-count"></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">活跃月份</span>
                        <span class="info-value" id="active-months">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-dashboard">
        <div class="charts-header">
            <h2 class="charts-title">
                <span class="charts-icon">📈</span>
                数据分析图表
            </h2>
            <p class="charts-subtitle">深入了解您的拍摄习惯和趋势</p>
        </div>
        
        <div class="charts-container">
            <!-- 内容分析图表 -->
            {% if analysis_report %}
            <div class="chart-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">📊 题材分布</h3>
                        <p class="chart-description">拍摄主题词云分析</p>
                    </div>
                    <div id="subject-wordcloud" class="chart-content wordcloud"></div>
                </div>
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">🌈 色调分析</h3>
                        <p class="chart-description">照片色彩倾向统计</p>
                    </div>
                    <div id="tone-distribution" class="chart-content"></div>
                </div>
            </div>
            {% endif %}
            
            <!-- 拍摄统计图表 -->
            <div class="chart-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">📅 月份趋势</h3>
                        <p class="chart-description">按月统计拍摄活动</p>
                    </div>
                    <div id="months-chart" class="chart-content"></div>
                </div>
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">🕐 时段分布</h3>
                        <p class="chart-description">一天中的拍摄偏好</p>
                    </div>
                    <div id="time-periods-chart" class="chart-content"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="dashboard-actions">
        <button onclick="analyzePhotos()" class="action-btn primary">
            <span class="btn-icon">🔄</span>
            <span class="btn-text">更新分析</span>
        </button>
        <a href="/data" class="action-btn secondary">
            <span class="btn-icon">📊</span>
            <span class="btn-text">详细数据</span>
        </a>
        <a href="/settings" class="action-btn outline">
            <span class="btn-icon">⚙️</span>
            <span class="btn-text">设置</span>
        </a>
    </div>
    
    {% endif %}
</div>

<!-- 全屏照片查看器 -->
<div id="photoViewer" class="photo-viewer-overlay" onclick="closePhotoViewer()">
    <div class="photo-viewer-content" onclick="event.stopPropagation()">
        <button class="photo-viewer-close" onclick="closePhotoViewer()">✕</button>
        <img id="photoViewerImage" class="photo-viewer-image" src="" alt="">
        <div id="photoViewerTitle" class="photo-viewer-title"></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{% if has_data %}
<script src="/static/js/content-analysis.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查Plotly是否正确加载
    if (typeof Plotly === 'undefined') {
        console.error('Plotly未正确加载！');
        alert('图表库加载失败，请检查网络连接后刷新页面。');
        return;
    }
    
    console.log('Plotly库加载成功，版本:', Plotly.version);
    console.log('正在加载图表数据...');
    
    // 增加延迟确保DOM完全加载
    setTimeout(loadOverviewCharts, 300);
    
    // 加载内容分析相关图表
    {% if analysis_report %}
    setTimeout(loadContentAnalysisCharts, 500);
    {% endif %}
});

// 全屏照片查看器功能 - 增强版
function viewFullPhoto(photoPath, photoName) {
    const viewer = document.getElementById('photoViewer');
    const image = document.getElementById('photoViewerImage');
    const title = document.getElementById('photoViewerTitle');
    
    // 设置图片和标题
    image.src = `/api/photo/${encodeURIComponent(photoPath)}`;
    title.textContent = photoName;
    
    // 显示查看器
    viewer.classList.add('active');
    
    // 阻止页面滚动
    document.body.style.overflow = 'hidden';
    
    // 添加加载动画
    image.onload = function() {
        this.style.opacity = '1';
    };
    
    image.style.opacity = '0';
    image.style.transition = 'opacity 0.3s ease';
}

function closePhotoViewer() {
    const viewer = document.getElementById('photoViewer');
    viewer.classList.remove('active');
    
    // 恢复页面滚动
    document.body.style.overflow = '';
}

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePhotoViewer();
    }
});
</script>
{% endif %}
{% endblock %}