{% extends "base.html" %}

{% block title %}主页 | 照片分析器{% endblock %}

{% block content %}
<div class="dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <span aria-hidden="true">📸</span>
            照片分析仪表板
        </h1>
        <p class="dashboard-subtitle">智能分析您的摄影作品，发现拍摄规律与精彩瞬间</p>
    </div>

    {% if not has_data %}
    <!-- Empty State -->
    <div class="empty-state">
        <div class="empty-icon" aria-hidden="true">📷</div>
        <h2 class="empty-title">开始您的摄影分析之旅</h2>
        <p class="empty-description">
            {% if config.paths|length > 0 %}
                已配置 {{ config.paths|length }} 个监控路径，点击下方按钮开始分析照片数据。
            {% else %}
                请先在设置页面添加包含照片的文件夹路径，我们将自动分析EXIF信息并生成详细的统计报告。
            {% endif %}
        </p>
        <div class="empty-actions">
            {% if config.paths|length > 0 %}
                <button onclick="analyzePhotos()" class="btn btn-primary btn-lg">
                    <span aria-hidden="true">🔍</span>
                    开始分析照片
                </button>
            {% endif %}
            <a href="/settings" class="btn btn-secondary btn-lg">
                <span aria-hidden="true">⚙️</span>
                管理设置
            </a>
        </div>
    </div>
    {% else %}

    <!-- Statistics Overview -->
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-icon" aria-hidden="true">📸</span>
            <div class="stat-number">{{ total_photos }}</div>
            <div class="stat-label">照片总数</div>
        </div>

        {% if analysis_report %}
        <div class="stat-card">
            <span class="stat-icon" aria-hidden="true">⭐</span>
            <div class="stat-number">{{ "%.1f"|format(analysis_report.平均评分) }}</div>
            <div class="stat-label">平均评分</div>
        </div>
        <div class="stat-card">
            <span class="stat-icon" aria-hidden="true">🏆</span>
            <div class="stat-number">{{ analysis_report.最高评分 }}</div>
            <div class="stat-label">最高评分</div>
        </div>
        <div class="stat-card">
            <span class="stat-icon" aria-hidden="true">📊</span>
            <div class="stat-number">{{ analysis_report.最低评分 }}</div>
            <div class="stat-label">最低评分</div>
        </div>
        {% endif %}

        {% if content_analysis_status and content_analysis_status.total > 0 %}
        <div class="stat-card">
            <span class="stat-icon" aria-hidden="true">✅</span>
            <div class="stat-number">{{ content_analysis_status.analyzed }}</div>
            <div class="stat-label">已分析</div>
        </div>
        <div class="stat-card">
            <span class="stat-icon" aria-hidden="true">⏳</span>
            <div class="stat-number">{{ content_analysis_status.unanalyzed }}</div>
            <div class="stat-label">待分析</div>
        </div>
        {% endif %}
    </div>

    <!-- Best Photos Section -->
    {% if analysis_report and analysis_report.评分最佳照片 %}
    <section class="card">
        <div class="card-header">
            <h2 class="card-title">
                <span aria-hidden="true">🏆</span>
                最佳照片展示
            </h2>
            <p class="card-subtitle">AI评分最高的精选作品</p>
        </div>

        <div class="photo-grid">
            {% for photo in analysis_report.评分最佳照片[:6] %}
            <div class="photo-card">
                <div class="photo-image">
                    <img
                        src="/api/photo-thumbnail/{{ photo.路径 | urlencode }}?width=300&height=200"
                        alt="{{ photo.文件名 }}"
                        loading="lazy"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                    >
                    <div class="photo-placeholder" style="display: none;">
                        <span aria-hidden="true">📷</span>
                        <span>无法加载</span>
                    </div>
                    <div class="photo-score">{{ photo.评分 }}</div>
                </div>
                <div class="photo-details">
                    <h3 class="photo-title">{{ photo.文件名 }}</h3>
                    <div class="photo-meta">
                        <div class="meta-item">
                            <span class="meta-icon" aria-hidden="true">📷</span>
                            <span class="meta-text">{{ photo.拍摄设备 or '未知设备' }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon" aria-hidden="true">🔍</span>
                            <span class="meta-text">{{ photo.镜头 or '未知镜头' }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon" aria-hidden="true">⚙️</span>
                            <span class="meta-text">{{ photo.光圈 }} · {{ photo.快门 }} · ISO{{ photo.ISO }}</span>
                        </div>
                    </div>
                    <button
                        class="btn btn-sm btn-outline w-full"
                        onclick="viewFullPhoto('{{ photo.路径 | urlencode }}', '{{ photo.文件名 }}')"
                    >
                        查看原图
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- Device and Lens Statistics -->
    <div class="grid grid-cols-1 gap-6" style="grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));">
        <!-- Device Statistics -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <span aria-hidden="true">📱</span>
                    设备统计
                </h3>
            </div>
            <div class="flex flex-col gap-4">
                <div class="flex justify-between items-center">
                    <span class="text-secondary">最常用设备</span>
                    <div class="text-right">
                        <span id="top-device" class="font-medium">加载中...</span>
                        <span id="top-device-count" class="text-sm text-muted"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lens Statistics -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <span aria-hidden="true">🔍</span>
                    镜头统计
                </h3>
            </div>
            <div class="flex flex-col gap-4">
                <div class="flex justify-between items-center">
                    <span class="text-secondary">最常用镜头</span>
                    <div class="text-right">
                        <span id="top-lens" class="font-medium">加载中...</span>
                        <span id="top-lens-count" class="text-sm text-muted"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shooting Preferences -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <span aria-hidden="true">⭐</span>
                    拍摄偏好
                </h3>
            </div>
            <div class="flex flex-col gap-4">
                <div class="flex justify-between items-center">
                    <span class="text-secondary">常用搭配</span>
                    <div class="text-right">
                        <span id="device-lens-combo" class="font-medium">加载中...</span>
                        <span id="device-lens-combo-count" class="text-sm text-muted"></span>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-secondary">活跃月份</span>
                    <span id="active-months" class="font-medium">加载中...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <section class="card">
        <div class="card-header">
            <h2 class="card-title">
                <span aria-hidden="true">📈</span>
                数据分析图表
            </h2>
            <p class="card-subtitle">深入了解您的拍摄习惯和趋势</p>
        </div>

        <div class="chart-grid">
            {% if analysis_report %}
            <!-- Content Analysis Charts -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <span aria-hidden="true">📊</span>
                        题材分布
                    </h3>
                    <p class="chart-description">拍摄主题词云分析</p>
                </div>
                <div id="subject-wordcloud" class="chart-content"></div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <span aria-hidden="true">🌈</span>
                        色调分析
                    </h3>
                    <p class="chart-description">照片色彩倾向统计</p>
                </div>
                <div id="tone-distribution" class="chart-content"></div>
            </div>
            {% endif %}

            <!-- Shooting Statistics Charts -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <span aria-hidden="true">📅</span>
                        月份趋势
                    </h3>
                    <p class="chart-description">按月统计拍摄活动</p>
                </div>
                <div id="months-chart" class="chart-content"></div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <span aria-hidden="true">🕐</span>
                        时段分布
                    </h3>
                    <p class="chart-description">一天中的拍摄偏好</p>
                </div>
                <div id="time-periods-chart" class="chart-content"></div>
            </div>
        </div>
    </section>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-4 justify-center mt-8">
        <button onclick="analyzePhotos()" class="btn btn-primary">
            <span aria-hidden="true">🔄</span>
            更新分析
        </button>
        <a href="/data" class="btn btn-secondary">
            <span aria-hidden="true">📊</span>
            详细数据
        </a>
        <a href="/settings" class="btn btn-outline">
            <span aria-hidden="true">⚙️</span>
            设置
        </a>
    </div>

    {% endif %}
{% endblock %}

{% block scripts %}
{% if has_data %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if Plotly is loaded correctly
    if (typeof Plotly === 'undefined') {
        console.error('Plotly library failed to load!');
        window.photoAnalyzer.showToast('图表库加载失败，请检查网络连接后刷新页面', 'error');
        return;
    }

    // Load content analysis charts if available
    {% if analysis_report %}
    loadContentAnalysisCharts();
    {% endif %}
});

// Load content analysis charts
function loadContentAnalysisCharts() {
    try {
        // Subject wordcloud
        const subjectData = {{ analysis_report.主题分布|tojson }};
        if (subjectData && document.getElementById('subject-wordcloud')) {
            createWordCloud('subject-wordcloud', subjectData);
        }

        // Tone distribution
        const toneData = {{ analysis_report.色调分布|tojson }};
        if (toneData && document.getElementById('tone-distribution')) {
            createToneChart('tone-distribution', toneData);
        }
    } catch (error) {
        console.error('Error loading content analysis charts:', error);
    }
}

// Create word cloud
function createWordCloud(elementId, data) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const words = Object.entries(data).map(([text, value]) => ({
        text,
        value: typeof value === 'number' ? value : 1
    }));

    const trace = {
        type: 'scatter',
        mode: 'text',
        text: words.map(d => d.text),
        x: words.map((_, i) => Math.random()),
        y: words.map((_, i) => Math.random()),
        textfont: {
            size: words.map(d => Math.max(12, Math.min(32, d.value * 5))),
            color: words.map(() => `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`)
        },
        hoverinfo: 'text'
    };

    const layout = {
        xaxis: { showgrid: false, zeroline: false, showticklabels: false },
        yaxis: { showgrid: false, zeroline: false, showticklabels: false },
        margin: { t: 10, b: 10, l: 10, r: 10 },
        hovermode: 'closest'
    };

    Plotly.newPlot(elementId, [trace], layout, { displayModeBar: false });
}

// Create tone distribution chart
function createToneChart(elementId, data) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const colors = Object.keys(data);
    const values = Object.values(data);

    const trace = {
        type: 'pie',
        labels: colors,
        values: values,
        textinfo: 'label+percent',
        insidetextorientation: 'radial',
        marker: {
            colors: colors.map(color => color.toLowerCase())
        }
    };

    const layout = {
        margin: { t: 10, b: 10, l: 10, r: 10 },
        showlegend: false
    };

    Plotly.newPlot(elementId, [trace], layout, { displayModeBar: false });
}
</script>
{% endif %}
{% endblock %}