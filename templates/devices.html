{% extends "base.html" %}

{% block title %}设备统计 | 照片分析器{% endblock %}

{% block content %}
<div class="card">
    <h1 class="card-title">设备统计面板</h1>
    <p style="text-align: center; color: #64748b;">拍摄设备和镜头使用情况统计</p>
</div>

<!-- 设备图表 -->
<div class="charts-section">
    <div class="charts-grid">
        <!-- 拍摄设备统计 -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>📱 拍摄设备统计</h3>
                <p class="chart-subtitle">按设备型号统计使用频率</p>
            </div>
            <div id="devices-chart" class="chart-content"></div>
            <div id="devices-analysis" class="analysis-card" style="display: none;">
                <h4><span>✨ 数据洞察</span></h4>
                <p class="analysis-text"></p>
            </div>
        </div>

        <!-- 镜头使用统计 -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>🔍 镜头使用统计</h3>
                <p class="chart-subtitle">按镜头型号统计使用频率</p>
            </div>
            <div id="lenses-chart" class="chart-content"></div>
            <div id="lenses-analysis" class="analysis-card" style="display: none;">
                <h4><span>✨ 数据洞察</span></h4>
                <p class="analysis-text"></p>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="actions-container">
    <button onclick="analyzePhotos()" class="btn btn-primary">🔄 重新分析</button>
    <a href="/" class="btn btn-secondary">🏠 返回主页</a>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载图表数据
    setTimeout(loadDeviceCharts, 100);
    
    // 加载已保存的分析结果
    setTimeout(loadSavedAnalysisResults, 300);
    
    // 检查LLM状态并自动分析图表
    setTimeout(checkLlmStatusAndAnalyze, 500);
});
</script>
{% endblock %}
