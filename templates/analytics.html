{% extends "new_base.html" %}

{% block title %}数据分析 | 照片分析器{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">数据分析</h1>
    <p class="page-subtitle">详细的拍摄参数统计数据和深度分析</p>
</div>

<!-- 数据图表网格 -->
<div class="grid grid-cols-2 gap-6">
    <!-- 焦距统计 -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">📏 焦距使用统计</h3>
            <p class="chart-description">按焦距范围统计使用频率</p>
        </div>
        <div id="focal-lengths-chart" class="chart-content"></div>
        <div id="focal_lengths-analysis" class="analysis-section" style="display: none; padding: var(--space-6); border-top: 1px solid var(--gray-200); background: var(--gray-50);">
            <h4 class="font-semibold text-gray-900 mb-2">✨ 数据洞察</h4>
            <p class="analysis-text text-gray-700"></p>
        </div>
    </div>

    <!-- 光圈统计 -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">🔆 光圈使用统计</h3>
            <p class="chart-description">按光圈值统计使用频率</p>
        </div>
        <div id="apertures-chart" class="chart-content"></div>
        <div id="apertures-analysis" class="analysis-section" style="display: none; padding: var(--space-6); border-top: 1px solid var(--gray-200); background: var(--gray-50);">
            <h4 class="font-semibold text-gray-900 mb-2">✨ 数据洞察</h4>
            <p class="analysis-text text-gray-700"></p>
        </div>
    </div>

    <!-- 快门速度统计 -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">⚡ 快门速度统计</h3>
            <p class="chart-description">按快门速度统计使用频率</p>
        </div>
        <div id="shutter-speeds-chart" class="chart-content"></div>
        <div id="shutter_speeds-analysis" class="analysis-section" style="display: none; padding: var(--space-6); border-top: 1px solid var(--gray-200); background: var(--gray-50);">
            <h4 class="font-semibold text-gray-900 mb-2">✨ 数据洞察</h4>
            <p class="analysis-text text-gray-700"></p>
        </div>
    </div>

    <!-- ISO统计 -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">📈 ISO使用统计</h3>
            <p class="chart-description">按ISO值统计使用频率</p>
        </div>
        <div id="iso-values-chart" class="chart-content"></div>
        <div id="iso_values-analysis" class="analysis-section" style="display: none; padding: var(--space-6); border-top: 1px solid var(--gray-200); background: var(--gray-50);">
            <h4 class="font-semibold text-gray-900 mb-2">✨ 数据洞察</h4>
            <p class="analysis-text text-gray-700"></p>
        </div>
    </div>

    <!-- 设备统计 -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">📱 拍摄设备统计</h3>
            <p class="chart-description">按设备型号统计使用频率</p>
        </div>
        <div id="devices-chart" class="chart-content"></div>
        <div id="devices-analysis" class="analysis-section" style="display: none; padding: var(--space-6); border-top: 1px solid var(--gray-200); background: var(--gray-50);">
            <h4 class="font-semibold text-gray-900 mb-2">✨ 数据洞察</h4>
            <p class="analysis-text text-gray-700"></p>
        </div>
    </div>

    <!-- 镜头统计 -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">🔍 镜头使用统计</h3>
            <p class="chart-description">按镜头型号统计使用频率</p>
        </div>
        <div id="lenses-chart" class="chart-content"></div>
        <div id="lenses-analysis" class="analysis-section" style="display: none; padding: var(--space-6); border-top: 1px solid var(--gray-200); background: var(--gray-50);">
            <h4 class="font-semibold text-gray-900 mb-2">✨ 数据洞察</h4>
            <p class="analysis-text text-gray-700"></p>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="flex gap-4 justify-center mt-8">
    <button onclick="analyzePhotos()" class="btn btn-primary">
        <span>🔄</span>
        重新分析
    </button>
    <a href="/" class="btn btn-secondary">
        <span>🏠</span>
        返回概览
    </a>
    <a href="/gallery" class="btn btn-outline">
        <span>🖼️</span>
        照片库
    </a>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载图表数据
    setTimeout(loadDataCharts, 100);
    setTimeout(loadDeviceCharts, 200);
    
    // 加载已保存的分析结果
    setTimeout(loadSavedAnalysisResults, 300);
    
    // 检查LLM状态并自动分析图表
    setTimeout(checkLlmStatusAndAnalyze, 500);
});
</script>
{% endblock %}