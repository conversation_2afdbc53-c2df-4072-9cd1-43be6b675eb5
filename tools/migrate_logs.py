#!/usr/bin/env python3
"""
迁移LLM日志工具
将旧格式的日志文件(data/logs/llm_calls.jsonl)转换为新格式并保存到新位置(logs/llm_calls.jsonl)
"""

import os
import json
import shutil
from datetime import datetime

# 旧日志和新日志路径
OLD_LOG_DIR = os.path.join("data", "logs")
OLD_LOG_FILE = os.path.join(OLD_LOG_DIR, "llm_calls.jsonl")
NEW_LOG_DIR = os.path.join("logs")
NEW_LOG_FILE = os.path.join(NEW_LOG_DIR, "llm_calls.jsonl")

def ensure_dir(dir_path):
    """确保目录存在"""
    if not os.path.exists(dir_path):
        os.makedirs(dir_path, exist_ok=True)
        print(f"创建目录: {dir_path}")

def migrate_logs():
    """迁移日志文件到新位置并转换格式"""
    # 检查旧日志是否存在
    if not os.path.exists(OLD_LOG_FILE):
        print(f"旧日志文件不存在: {OLD_LOG_FILE}")
        return False
    
    # 确保新日志目录存在
    ensure_dir(NEW_LOG_DIR)
    
    # 如果新日志已经存在，备份它
    if os.path.exists(NEW_LOG_FILE):
        backup_file = f"{NEW_LOG_FILE}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        shutil.copy2(NEW_LOG_FILE, backup_file)
        print(f"已备份现有日志文件: {backup_file}")
    
    # 读取旧日志并转换格式
    converted_entries = []
    with open(OLD_LOG_FILE, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for line in lines:
            try:
                entry = json.loads(line.strip())
                
                # 提取关键内容
                prompt = ""
                if "inputs" in entry:
                    inputs = entry["inputs"]
                    if "prompt" in inputs:
                        prompt = inputs["prompt"]
                    elif "self" in inputs:
                        prompt = inputs["self"]
                
                # 创建简化的日志条目
                simplified_entry = {
                    "timestamp": entry.get("timestamp", ""),
                    "function": entry.get("function", ""),
                    "prompt": prompt,
                    "response": entry.get("outputs", ""),
                }
                
                # 如果有错误，添加错误信息
                if "error" in entry:
                    simplified_entry["error"] = entry["error"]
                
                converted_entries.append(simplified_entry)
            except json.JSONDecodeError:
                print(f"跳过无效的JSON行: {line[:50]}...")
                continue
    
    # 写入新日志文件
    with open(NEW_LOG_FILE, 'w', encoding='utf-8') as f:
        for entry in converted_entries:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"成功将 {len(converted_entries)} 条日志记录迁移到新位置: {NEW_LOG_FILE}")
    return True

if __name__ == "__main__":
    print("开始迁移LLM日志...")
    result = migrate_logs()
    if result:
        print("日志迁移完成!")
    else:
        print("日志迁移失败!")
