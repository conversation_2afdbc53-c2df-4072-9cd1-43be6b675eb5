#!/usr/bin/env python3
"""
LLM日志查看器
用于在命令行查看LLM调用日志
"""

import os
import json
import sys
import argparse
from datetime import datetime

# 日志文件路径
LOG_DIR = os.path.join("logs")
LLM_LOG_FILE = os.path.join(LOG_DIR, "llm_calls.jsonl")

def format_log_entry(entry, verbose=False):
    """格式化日志条目为可读文本"""
    timestamp = entry.get("timestamp", "")
    try:
        dt = datetime.fromisoformat(timestamp)
        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, TypeError):
        formatted_time = timestamp
    
    function = entry.get("function", "未知函数")
    prompt = entry.get("prompt", "")
    response = entry.get("response", "")
    
    if isinstance(response, dict):
        response = json.dumps(response, ensure_ascii=False, indent=2)
        
    # 限制长度
    max_length = 1000 if verbose else 100
    if len(prompt) > max_length and not verbose:
        prompt = prompt[:max_length] + "..."
    if len(response) > max_length and not verbose:
        response = response[:max_length] + "..."
    
    result = f"[{formatted_time}] {function}\n"
    result += f"提示词: {prompt}\n"
    result += f"响应: {response}\n"
    
    if "error" in entry:
        result += f"错误: {entry['error']}\n"
    
    result += "-" * 80 + "\n"
    return result

def view_logs(limit=10, verbose=False):
    """查看日志文件"""
    if not os.path.exists(LLM_LOG_FILE):
        print(f"日志文件不存在: {LLM_LOG_FILE}")
        return False
    
    with open(LLM_LOG_FILE, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        
        if not lines:
            print("日志文件为空")
            return True
        
        # 只显示最后limit行
        entries = []
        for line in lines[-limit:]:
            try:
                entry = json.loads(line.strip())
                entries.append(entry)
            except json.JSONDecodeError:
                print(f"跳过无效的JSON行")
                continue
        
        # 如果没有有效条目
        if not entries:
            print("没有有效的日志条目")
            return True
        
        # 打印日志条目
        print(f"\n共找到 {len(entries)} 条日志记录:")
        print("=" * 80)
        
        for entry in entries:
            print(format_log_entry(entry, verbose))
        
        return True

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="LLM日志查看器")
    parser.add_argument("-n", "--limit", type=int, default=10, 
                        help="显示的日志条目数量 (默认: 10)")
    parser.add_argument("-v", "--verbose", action="store_true",
                        help="显示完整日志内容，不截断")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    print(f"查看LLM调用日志 (最新 {args.limit} 条记录):")
    view_logs(limit=args.limit, verbose=args.verbose)
