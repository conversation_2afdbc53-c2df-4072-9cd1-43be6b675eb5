from fastapi import FastAPI, Request, Form, File, UploadFile, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
import os
import json
import pandas as pd
import shutil
import re
import time
import base64
from urllib.parse import quote, unquote
import mimetypes
from modules.llm_logger import LLM_LOG_FILE  # 导入LLM日志文件路径
from pathlib import Path
from typing import List, Optional
from modules.batch_photo_analyzer import BatchPhotoAnalyzer
from modules.basic_analysis import BasicPhotoAnalyzer
from modules.chart_data_processor import PhotoDataProcessor, ChartGenerator
from modules.llm_config import get_llm_config, save_llm_config
from modules.llm_api import OllamaLLMAPI
from modules.analysis_storage import (
    get_analysis_results, save_analysis_result, 
    get_analysis_for_chart, is_analysis_outdated, clear_all_analysis
)
from modules.async_tasks import (
    submit_chart_analysis_task, get_task_status, 
    init_task_worker, STATUS_COMPLETED, STATUS_FAILED
)
from modules.content_auto_checker import <PERSON><PERSON><PERSON><PERSON>he<PERSON>, check_and_auto_analyze, get_current_analysis_status

app = FastAPI(title="照片分析器", description="照片EXIF数据分析工具")

# 启动异步任务处理线程
init_task_worker()

# 静态文件和模板设置
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 配置文件和数据文件路径
config_file = "./config/config.json"  # 更新配置文件路径
raw_data_file = "./data/raw.csv"
analysis_data_file = "./data/basic_analysis.csv"

def load_config():
    """加载配置文件"""
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {"paths": []}

def save_config(config):
    """保存配置文件"""
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def ensure_data_directory():
    """确保数据目录存在"""
    os.makedirs("./data", exist_ok=True)

def analyze_all_photos():
    """分析所有配置路径下的照片"""
    config = load_config()
    paths = config.get("paths", [])
    
    if not paths:
        return False, "没有配置照片路径"
    
    ensure_data_directory()
    
    try:
        # 第一步：使用BatchPhotoAnalyzer提取所有照片的原始数据
        batch_analyzer = BatchPhotoAnalyzer()
        
        # 删除旧的原始数据文件，重新生成
        if os.path.exists(raw_data_file):
            os.remove(raw_data_file)
        
        # 处理每个配置的路径
        for path in paths:
            if os.path.exists(path):
                print(f"正在分析路径: {path}")
                batch_analyzer.process_directory(path, raw_data_file)
        
        # 检查是否生成了原始数据
        if not os.path.exists(raw_data_file):
            return False, "未找到任何照片或分析失败"
        
        # 第二步：使用BasicPhotoAnalyzer对原始数据进行统计分析
        basic_analyzer = BasicPhotoAnalyzer()
        analysis_results = basic_analyzer.analyze_photos(raw_data_file)
        
        if not analysis_results:
            return False, "统计分析失败"
        
        # 第三步：将统计结果保存到CSV文件
        basic_analyzer.save_results_to_csv(analysis_results, analysis_data_file)
        
        return True, f"分析完成，共处理了 {analysis_results['总体统计']['总照片数量']} 张照片"
        
    except Exception as e:
        return False, f"分析过程中发生错误: {str(e)}"

def get_chart_data_processor():
    """获取图表数据处理器"""
    processor = PhotoDataProcessor(analysis_data_file)
    if processor.load_and_parse_csv():
        processor.process_data_for_charts()
        return processor
    return None

def get_chart_generator():
    """获取图表生成器"""
    processor = get_chart_data_processor()
    if processor:
        return ChartGenerator(processor)
    return None

def get_chart_data():
    """获取统一的图表数据"""
    processor = get_chart_data_processor()
    if processor:
        return {
            'total_photos': processor.raw_data.get('total_photos', 0),
            'focal_lengths': processor.processed_data.get('focal_lengths', {}),
            'apertures': processor.processed_data.get('apertures', {}),
            'shutter_speeds': processor.processed_data.get('shutter_speeds', {}),
            'iso_values': processor.processed_data.get('iso_values', {}),
            'devices': processor.processed_data.get('devices', {}),
            'lenses': processor.processed_data.get('lenses', {}),
            'months': processor.processed_data.get('months', {}),
            'time_periods': processor.processed_data.get('time_periods', {})
        }
    return None

# 导入AI分析模块
from modules.ai_analyzer import analyze_chart_data, text_to_structured_format, batch_analyze_all_charts

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    config = load_config()
    
    # 检查是否有分析数据
    chart_data = get_chart_data()
    
    has_data = chart_data is not None and chart_data.get('total_photos', 0) > 0
    total_photos = chart_data.get('total_photos', 0) if chart_data else 0
    
    # 自动检查和分析未分析的照片
    analysis_report = None
    content_analysis_status = {"total": 0, "analyzed": 0, "unanalyzed": 0}
    
    try:
        # 获取内容分析状态
        content_analysis_status = get_current_analysis_status()
        print(f"📊 内容分析状态: {content_analysis_status}")
        
        # 如果有未分析的照片，在后台自动分析
        if content_analysis_status["unanalyzed"] > 0:
            import threading
            
            def auto_analyze():
                try:
                    print(f"🤖 后台自动检查内容分析，发现 {content_analysis_status['unanalyzed']} 张未分析照片...")
                    # 移除数量限制，分析所有未分析的照片
                    check_and_auto_analyze(max_analyze=None)
                except Exception as e:
                    print(f"后台自动分析失败: {str(e)}")
            
            # 在后台线程中执行自动分析
            threading.Thread(target=auto_analyze, daemon=True).start()
        
        # 获取分析报告
        checker = ContentAutoChecker()
        analysis_report = checker.get_analysis_report()
        
        # 传统的图表分析
        if has_data:
            llm_config = get_llm_config()
            if llm_config.get("enabled", False):
                print("🤖 后台自动检查AI分析...")
                batch_analyze_all_charts(force_new=False)
    except Exception as e:
        print(f"启动自动分析失败: {str(e)}")
    
    return templates.TemplateResponse("index.html", {
        "request": request,
        "has_data": has_data,
        "total_photos": total_photos,
        "config": config,
        "analysis_report": analysis_report,
        "content_analysis_status": content_analysis_status
    })

@app.get("/api/overview-charts")
async def get_overview_charts():
    """获取主页概览图表数据"""
    try:
        generator = get_chart_generator()
        processor = get_chart_data_processor()

        charts = {}

        if generator and processor:
            # 月份统计曲线图
            months_chart = generator.create_line_chart('months', '拍摄月份分布', '月份', '照片数量')
            if months_chart:
                charts['months'] = months_chart

            # 时段统计曲线图
            time_periods_chart = generator.create_line_chart('time_periods', '拍摄时段分布', '时段', '照片数量')
            if time_periods_chart:
                charts['time_periods'] = time_periods_chart

            # 获取设备镜头统计信息
            device_lens_info = processor.get_top_device_and_lens_info()
            charts['device_lens_info'] = device_lens_info
        else:
            # 没有数据时返回空的设备镜头信息
            charts['device_lens_info'] = {
                'top_device': {'name': '暂无数据', 'count': 0},
                'top_lens': {'name': '暂无数据', 'count': 0},
                'top_combo': {'name': '暂无数据', 'count': 0},
                'active_months': []
            }

        return JSONResponse(content=charts)
    except Exception as e:
        import traceback
        traceback.print_exc()
        # 即使出错也返回空数据而不是抛出异常
        return JSONResponse(content={
            'device_lens_info': {
                'top_device': {'name': '加载失败', 'count': 0},
                'top_lens': {'name': '加载失败', 'count': 0},
                'top_combo': {'name': '加载失败', 'count': 0},
                'active_months': []
            }
        })

@app.get("/data", response_class=HTMLResponse)
async def data_panel(request: Request):
    """数据面板"""
    return templates.TemplateResponse("data.html", {"request": request})

@app.get("/api/data-charts")
async def get_data_charts():
    """获取数据面板图表"""
    try:
        generator = get_chart_generator()
        charts = {}

        if generator:
            # 焦距柱形图
            focal_chart = generator.create_bar_chart('focal_lengths', '焦距使用统计',
                                                    '焦距范围', '使用次数', sort_by_value=True)
            if focal_chart:
                charts['focal_lengths'] = focal_chart

            # 光圈柱形图
            aperture_chart = generator.create_bar_chart('apertures', '光圈使用统计',
                                                       '光圈范围', '使用次数')
            if aperture_chart:
                charts['apertures'] = aperture_chart

            # 快门速度柱形图
            shutter_chart = generator.create_bar_chart('shutter_speeds', '快门速度使用统计',
                                                      '快门速度范围', '使用次数')
            if shutter_chart:
                charts['shutter_speeds'] = shutter_chart

            # ISO柱形图
            iso_chart = generator.create_bar_chart('iso_values', 'ISO使用统计',
                                                  'ISO范围', '使用次数')
            if iso_chart:
                charts['iso_values'] = iso_chart
        
        return JSONResponse(content=charts)

    except Exception as e:
        print(f"生成图表时出错: {str(e)}")
        # 返回空数据而不是抛出异常
        return JSONResponse(content={})

@app.get("/devices", response_class=HTMLResponse)
async def devices_panel(request: Request):
    """设备面板"""
    return templates.TemplateResponse("devices.html", {"request": request})

@app.get("/api/device-charts")
async def get_device_charts():
    """获取设备面板图表"""
    try:
        generator = get_chart_generator()
        charts = {}

        if generator:
            # 拍摄设备饼图
            devices_chart = generator.create_pie_chart('devices', '拍摄设备统计')
            if devices_chart:
                charts['devices'] = devices_chart

            # 镜头统计饼图
            lenses_chart = generator.create_pie_chart('lenses', '镜头使用统计')
            if lenses_chart:
                charts['lenses'] = lenses_chart

        return JSONResponse(content=charts)
    except Exception as e:
        print(f"生成设备图表时出错: {str(e)}")
        # 返回空数据而不是抛出异常
        return JSONResponse(content={})

@app.get("/settings", response_class=HTMLResponse)
async def get_settings_page(request: Request):
    """设置页面"""
    config = load_config()
    paths = config.get("paths", [])
    
    # 统计照片数量
    total_photos = 0
    if os.path.exists(analysis_data_file):
        try:
            analysis_df = pd.read_csv(analysis_data_file)
            if '总照片数量' in analysis_df.columns:
                total_photos = analysis_df['总照片数量'].iloc[0]
        except:
            pass
    
    # 计算数据文件大小
    data_size = "-"
    if os.path.exists("./data"):
        total_size = 0
        for path, dirs, files in os.walk("./data"):
            for f in files:
                fp = os.path.join(path, f)
                total_size += os.path.getsize(fp)
        
        # 转换为合适的单位
        if total_size < 1024:
            data_size = f"{total_size} B"
        elif total_size < 1024 * 1024:
            data_size = f"{total_size / 1024:.2f} KB"
        elif total_size < 1024 * 1024 * 1024:
            data_size = f"{total_size / (1024 * 1024):.2f} MB"
        else:
            data_size = f"{total_size / (1024 * 1024 * 1024):.2f} GB"
    
    # 获取LLM配置
    llm_config = get_llm_config()
    
    return templates.TemplateResponse("settings.html", {
        "request": request, 
        "paths": paths,
        "total_photos": total_photos,
        "data_size": data_size,
        "llm_config": llm_config
    })

@app.post("/api/analyze")
async def analyze_photos():
    """开始分析照片"""
    try:
        success, message = analyze_all_photos()
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": message
            })
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@app.post("/api/add-path")
async def add_path(path: str = Form(...)):
    """添加照片路径"""
    try:
        # 路径清理和标准化
        path = path.strip().replace('/', '\\')  # Windows路径标准化
        
        # 基本验证
        if not path:
            raise HTTPException(status_code=400, detail="路径不能为空")
        
        if len(path) < 3:
            raise HTTPException(status_code=400, detail="请输入有效的路径")
        
        # 检查路径是否存在
        if not os.path.exists(path):
            raise HTTPException(status_code=400, detail=f"路径不存在: {path}")
        
        # 检查是否为目录
        if not os.path.isdir(path):
            raise HTTPException(status_code=400, detail=f"指定的路径不是一个文件夹: {path}")
        
        # 检查路径是否可读
        try:
            os.listdir(path)
        except PermissionError:
            raise HTTPException(status_code=400, detail=f"没有访问该路径的权限: {path}")
        
        # 加载配置并添加路径
        config = load_config()
        if path not in config["paths"]:
            config["paths"].append(path)
            save_config(config)
            return JSONResponse(content={
                "success": True, 
                "message": f"路径添加成功: {path}"
            })
        else:
            return JSONResponse(content={
                "success": False, 
                "message": f"路径已存在: {path}"
            })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加路径失败: {str(e)}")

@app.post("/api/remove-path")
async def remove_path(path: str = Form(...)):
    """删除照片路径"""
    try:
        config = load_config()
        if path in config["paths"]:
            config["paths"].remove(path)
            save_config(config)
        
        return JSONResponse(content={"success": True, "message": "路径删除成功"})
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除路径失败: {str(e)}")

@app.get("/api/config")
async def get_config():
    """获取当前配置"""
    config = load_config()
    return JSONResponse(content=config)

@app.get("/api/content-analysis-report")
async def get_content_analysis_report():
    """获取内容分析报告"""
    try:
        checker = ContentAutoChecker()
        
        # 获取分析状态
        has_unanalyzed, status = checker.check_unanalyzed_photos()
        
        # 获取分析报告
        report = checker.get_analysis_report()
        
        return JSONResponse(content={
            "status": status,
            "has_unanalyzed": has_unanalyzed,
            "report": report
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分析报告失败: {str(e)}")

@app.post("/api/trigger-content-analysis")
async def trigger_content_analysis(max_analyze: int = 20):
    """手动触发内容分析"""
    try:
        import threading
        
        def analyze_task():
            try:
                analyzed, report = check_and_auto_analyze(max_analyze)
                print(f"分析完成，是否执行了新分析: {analyzed}")
            except Exception as e:
                print(f"分析失败: {str(e)}")
        
        # 在后台线程中执行分析
        threading.Thread(target=analyze_task, daemon=True).start()
        
        return JSONResponse(content={
            "success": True,
            "message": f"已启动内容分析任务，最多分析 {max_analyze} 张照片"
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发分析失败: {str(e)}")

@app.post("/api/clear-paths")
async def clear_all_paths():
    """清空所有路径"""
    try:
        config = load_config()
        config["paths"] = []
        save_config(config)
        return JSONResponse(content={"success": True, "message": "所有路径已清空"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空路径失败: {str(e)}")

@app.post("/api/reset-settings")
async def reset_settings():
    """重置所有设置"""
    try:
        # 创建默认配置
        default_config = {"paths": []}
        save_config(default_config)
        return JSONResponse(content={"success": True, "message": "设置已重置为默认值"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置设置失败: {str(e)}")

@app.post("/api/clear-cache")
async def clear_cache():
    """清除缓存数据"""
    try:
        # 删除数据文件
        if os.path.exists(raw_data_file):
            os.remove(raw_data_file)
        
        if os.path.exists(analysis_data_file):
            os.remove(analysis_data_file)
            
        return JSONResponse(content={"success": True, "message": "缓存数据已清除"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@app.post("/api/export-settings")
async def export_settings():
    """导出设置"""
    try:
        config = load_config()
        return JSONResponse(content={"success": True, "config": config})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出设置失败: {str(e)}")

@app.post("/api/import-settings")
async def import_settings(config_data: dict):
    """导入设置"""
    try:
        # 验证导入的数据格式
        if "paths" not in config_data:
            raise HTTPException(status_code=400, detail="无效的配置数据格式")
            
        # 验证路径列表
        if not isinstance(config_data["paths"], list):
            raise HTTPException(status_code=400, detail="路径数据格式不正确")
            
        # 过滤不存在的路径
        valid_paths = []
        for path in config_data["paths"]:
            if os.path.exists(path) and os.path.isdir(path):
                valid_paths.append(path)
                
        config_data["paths"] = valid_paths
        save_config(config_data)
        
        return JSONResponse(content={
            "success": True, 
            "message": f"设置导入成功，有效路径数量: {len(valid_paths)}"
        })
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入设置失败: {str(e)}")
@app.post("/api/upload-photos")
async def upload_photos(photos: List[UploadFile] = File(...)):
    """上传照片并分析"""
    try:
        if not photos:
            raise HTTPException(status_code=400, detail="没有选择照片文件")
        
        ensure_data_directory()
        
        # 创建临时目录存储上传的照片
        upload_dir = "./data/uploads"
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存上传的照片
        saved_files = []
        folder_structure = {}  # 记录文件夹结构
        
        for photo in photos:
            # 验证文件类型
            if not photo.content_type or not photo.content_type.startswith('image/'):
                print(f"跳过非图片文件: {photo.filename}")
                continue
                
            # 验证文件扩展名
            file_extension = Path(photo.filename).suffix.lower()
            if file_extension not in ['.jpg', '.jpeg', '.png', '.tiff', '.tif']:
                print(f"跳过不支持的格式: {photo.filename}")
                continue
            
            # 处理文件路径，保持文件夹结构
            filename = photo.filename
            if '/' in filename:  # 如果是从文件夹上传的，保持目录结构
                # 创建子目录
                file_dir = os.path.dirname(filename)
                full_dir = os.path.join(upload_dir, file_dir)
                os.makedirs(full_dir, exist_ok=True)
                
                file_path = os.path.join(upload_dir, filename)
                folder_name = filename.split('/')[0]
                
                if folder_name not in folder_structure:
                    folder_structure[folder_name] = []
                folder_structure[folder_name].append(filename)
            else:
                file_path = os.path.join(upload_dir, filename)
            
            # 保存文件
            try:
                with open(file_path, "wb") as buffer:
                    content = await photo.read()
                    buffer.write(content)
                saved_files.append(file_path)
                print(f"保存文件: {filename}")
            except Exception as e:
                print(f"保存文件失败 {filename}: {str(e)}")
                continue
        
        if not saved_files:
            raise HTTPException(status_code=400, detail="没有有效的照片文件")
        
        print(f"共保存 {len(saved_files)} 个文件")
        if folder_structure:
            print(f"文件夹结构: {list(folder_structure.keys())}")
        
        # 使用BatchPhotoAnalyzer分析上传的照片
        batch_analyzer = BatchPhotoAnalyzer()
        
        # 分析每个上传的文件
        analyzed_count = 0
        for file_path in saved_files:
            try:
                # 分析单个文件并追加到raw.csv
                if batch_analyzer.process_single_file(file_path, raw_data_file):
                    analyzed_count += 1
            except Exception as e:
                print(f"分析文件 {file_path} 失败: {str(e)}")
                continue
        
        # 清理临时文件和目录
        import shutil
        try:
            shutil.rmtree(upload_dir)
            print("清理临时文件完成")
        except Exception as e:
            print(f"清理临时文件失败: {str(e)}")
        
        if analyzed_count == 0:
            raise HTTPException(status_code=400, detail="没有成功分析任何照片")
        
        # 重新生成统计数据
        if os.path.exists(raw_data_file):
            basic_analyzer = BasicPhotoAnalyzer()
            analysis_results = basic_analyzer.analyze_photos(raw_data_file)
            
            if analysis_results:
                basic_analyzer.save_results_to_csv(analysis_results, analysis_data_file)
        
        # 生成结果消息
        message = f"成功上传并分析了 {analyzed_count} 张照片"
        if folder_structure:
            folder_info = ", ".join([f"{folder}({len(files)}张)" for folder, files in folder_structure.items()])
            message += f"，来自文件夹: {folder_info}"
        
        return JSONResponse(content={
            "success": True,
            "message": message
        })
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"上传处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传处理失败: {str(e)}")

# LLM设置API
@app.post("/api/save-llm-settings")
async def save_llm_settings_api(request: Request, api_url: str = Form(...), model: str = Form(...), enabled: str = Form(...)):
    """保存LLM设置"""
    try:
        # 转换enabled字符串为布尔值
        enabled_bool = True if enabled.lower() == 'true' else False
        
        llm_config = {
            "api_url": api_url,
            "model": model,
            "enabled": enabled_bool
        }
        
        if save_llm_config(llm_config):
            return JSONResponse(content={"success": True, "message": "LLM设置已保存"})
        else:
            return JSONResponse(content={"success": False, "message": "保存LLM设置失败"})
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"处理请求时出错: {str(e)}"})

@app.post("/api/test-llm-connection")
async def test_llm_connection_api(request: Request, api_url: str = Form(...), model: str = Form(...)):
    """测试LLM连接"""
    try:
        api = OllamaLLMAPI(base_url=api_url, model=model)
        response = api.generate_text("你好，这是一个测试。请简短回复。", system_prompt="请简短回复。")
        
        if response:
            return JSONResponse(content={"success": True, "message": f"连接成功，模型响应: {response[:30]}..."})
        else:
            return JSONResponse(content={"success": False, "message": "模型未返回响应"})
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"连接失败: {str(e)}"})

@app.post("/api/analyze-chart")
async def analyze_chart_api(request: Request, chart_type: str = Form(...), chart_data: str = Form(...), force_new: bool = Form(False)):
    """提交图表异步分析任务"""
    try:
        # 如果非强制分析，先检查缓存
        if not force_new:
            # 获取数据时间戳
            data_timestamp = int(time.time())
            try:
                data_obj = json.loads(chart_data)
                if isinstance(data_obj, dict) and 'timestamp' in data_obj:
                    data_timestamp = data_obj['timestamp']
            except:
                pass
                
            # 检查是否有缓存的分析结果
            results = get_analysis_results()
            if chart_type in results and not is_analysis_outdated(chart_type, data_timestamp):
                result_data = results[chart_type]
                
                # 优先使用缓存的结构化数据
                if "structured" in result_data:
                    return JSONResponse(content={
                        "success": True, 
                        "analysis": result_data.get("analysis", ""),
                        "structured": result_data["structured"],
                        "cached": True,
                        "status": "completed",
                        "task_id": None
                    })
                # 如果没有结构化数据，转换文本分析
                elif "analysis" in result_data:
                    structured_result = text_to_structured_format(result_data["analysis"])
                    return JSONResponse(content={
                        "success": True, 
                        "analysis": result_data["analysis"], 
                        "structured": structured_result,
                        "cached": True,
                        "status": "completed",
                        "task_id": None
                    })
        
        # 提交异步分析任务
        task_id = submit_chart_analysis_task(chart_type, chart_data, force_new)
        
        return JSONResponse(content={
            "success": True,
            "status": "pending",
            "message": "分析任务已提交",
            "task_id": task_id
        })
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"处理请求时出错: {str(e)}"})

@app.get("/api/llm-status")
async def get_llm_status(request: Request):
    """获取LLM状态"""
    try:
        llm_config = get_llm_config()
        return JSONResponse(content={
            "enabled": llm_config.get("enabled", False),
            "api_url": llm_config.get("api_url", ""),
            "model": llm_config.get("model", "")
        })
    except Exception as e:
        return JSONResponse(content={"enabled": False, "message": f"获取LLM状态失败: {str(e)}"})
        
@app.get("/api/analysis-results")
async def get_all_analysis_results(request: Request):
    """获取所有保存的分析结果"""
    try:
        # 获取原始分析结果
        results = get_analysis_results()
        
        # 转换为结构化格式
        structured_results = {}
        for chart_type, result in results.items():
            if "analysis" in result:
                try:
                    # 转换文本分析为结构化格式
                    structured = text_to_structured_format(result["analysis"])
                    structured_results[chart_type] = {
                        "analysis": result["analysis"],
                        "structured": structured,
                        "timestamp": result.get("timestamp", 0)
                    }
                except Exception:
                    # 保持原有格式
                    structured_results[chart_type] = result
            else:
                structured_results[chart_type] = result
        
        return JSONResponse(content=structured_results)
    except Exception as e:
        return JSONResponse(content={"error": f"获取分析结果失败: {str(e)}"})

@app.post("/api/clear-analysis")
async def clear_analysis_api(request: Request):
    """清除所有分析结果"""
    try:
        success = clear_all_analysis()
        if success:
            return JSONResponse(content={"success": True, "message": "分析结果已清除"})
        else:
            return JSONResponse(content={"success": False, "message": "清除分析结果失败"})
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"处理请求失败: {str(e)}"})
        
@app.get("/api/task-status/{task_id}")
async def get_task_status_api(request: Request, task_id: str):
    """获取任务状态"""
    try:
        task_info = get_task_status(task_id)
        
        if task_info["status"] is None:
            return JSONResponse(content={
                "success": False,
                "message": "任务不存在"
            })
        
        # 对于已完成的任务，返回完整结果
        if task_info["status"] == STATUS_COMPLETED and "result" in task_info:
            return JSONResponse(content={
                "success": True,
                "status": task_info["status"],
                "result": task_info["result"]
            })
        
        # 对于失败的任务，返回错误信息
        if task_info["status"] == STATUS_FAILED:
            error_msg = "分析失败"
            if "result" in task_info and "error" in task_info["result"]:
                error_msg = task_info["result"]["error"]
                
            return JSONResponse(content={
                "success": False,
                "status": task_info["status"],
                "message": error_msg
            })
        
        # 对于其他状态（如pending, processing），只返回状态
        return JSONResponse(content={
            "success": True,
            "status": task_info["status"],
            "message": f"任务状态: {task_info['status']}"
        })
        
    except Exception as e:
        return JSONResponse(content={
            "success": False,
            "message": f"获取任务状态失败: {str(e)}"
        })
        
@app.get("/api/llm-logs")
async def get_llm_logs(request: Request, limit: int = 50):
    """获取LLM调用日志，优化后只显示对话内容"""
    try:
        if os.path.exists(LLM_LOG_FILE):
            logs = []
            with open(LLM_LOG_FILE, 'r', encoding='utf-8') as f:
                # 读取最后limit行的日志
                lines = f.readlines()
                for line in lines[-limit:]:
                    try:
                        log_entry = json.loads(line.strip())
                        # 简化日志显示
                        simplified_entry = {
                            "timestamp": log_entry.get("timestamp", ""),
                            "function": log_entry.get("function", ""),
                            "prompt": log_entry.get("prompt", ""),
                            "response": log_entry.get("response", "")
                        }
                        if "error" in log_entry:
                            simplified_entry["error"] = log_entry["error"]
                        
                        logs.append(simplified_entry)
                    except json.JSONDecodeError:
                        continue
            return JSONResponse(content={"success": True, "logs": logs})
        else:
            return JSONResponse(content={"success": False, "message": "日志文件不存在"})
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"获取日志失败: {str(e)}"})
        
@app.post("/api/batch-analyze")
async def batch_analyze_charts(request: Request):
    """批量分析所有图表"""
    try:
        # 获取请求参数
        data = await request.json()
        force_new = data.get("force_new", False)
        
        # 执行批量分析
        result = batch_analyze_all_charts(force_new)
        
        return JSONResponse(content=result)
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"批量分析失败: {str(e)}"})

@app.get("/api/auto-analyze")
async def auto_analyze_charts(request: Request):
    """自动分析需要更新的图表"""
    try:
        # 执行自动分析（不强制重新分析）
        result = batch_analyze_all_charts(force_new=False)
        
        return JSONResponse(content=result)
    except Exception as e:
        return JSONResponse(content={"success": False, "message": f"自动分析失败: {str(e)}"})
        
@app.get("/api/content-analysis-status")
async def get_content_analysis_status():
    """获取内容分析状态"""
    try:
        status = get_current_analysis_status()
        return JSONResponse(content={
            "success": True,
            "data": status
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分析状态失败: {str(e)}")

@app.post("/api/generate-analysis-report")
async def generate_analysis_report():
    """生成分析报告"""
    try:
        from modules.content_analyzer import ContentAnalyzer
        analyzer = ContentAnalyzer()
        analyzer.create_analysis_report()
        
        return JSONResponse(content={
            "success": True,
            "message": "分析报告生成成功"
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")
        
@app.get("/api/photo/{photo_path:path}")
async def get_photo(photo_path: str):
    """获取照片文件"""
    try:
        # URL解码路径
        decoded_path = unquote(photo_path)
        
        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail="照片文件不存在")
        
        # 检查是否是图片文件
        mime_type, _ = mimetypes.guess_type(decoded_path)
        if not mime_type or not mime_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="不是有效的图片文件")
        
        # 返回文件
        return FileResponse(
            path=decoded_path,
            media_type=mime_type,
            headers={"Cache-Control": "public, max-age=3600"}  # 缓存1小时
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取照片失败: {str(e)}")

@app.get("/api/photo-thumbnail/{photo_path:path}")
async def get_photo_thumbnail(photo_path: str, width: int = 400, height: int = 300):
    """获取照片缩略图"""
    try:
        from PIL import Image
        import io
        from fastapi.responses import Response
        
        # URL解码路径
        decoded_path = unquote(photo_path)
        
        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail="照片文件不存在")
        
        # 打开图片并生成缩略图
        with Image.open(decoded_path) as img:
            # 保持长宽比的缩略图
            img.thumbnail((width, height), Image.Resampling.LANCZOS)
            
            # 转换为RGB模式（去除透明通道）
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # 保存到内存
            img_io = io.BytesIO()
            img.save(img_io, format='JPEG', quality=85, optimize=True)
            img_io.seek(0)
            
            # 返回缩略图
            return Response(
                content=img_io.getvalue(),
                media_type="image/jpeg",
                headers={"Cache-Control": "public, max-age=3600"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成缩略图失败: {str(e)}")
        
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
