

### **优化后的设置界面结构和逻辑描述**

本优化旨在提升用户体验，使设置功能更具逻辑性、易用性，并清晰地划分各模块职责。

#### **一、设置主页 (入口)**

*   **标题：** 设置
*   **副标题：** 管理照片文件夹、AI分析、数据与系统设置。
*   **主要模块（卡片式布局，共四个清晰的入口）：**

    1.  **文件与导入 (Files & Import)**
        *   **图标：** 文件夹与向上箭头的组合图标 (代表文件夹管理与文件导入)。
        *   **描述：** 管理照片的来源（扫描文件夹）和直接导入（上传照片）。
        *   **作用：** 汇集所有将照片引入系统进行分析的方式。

    2.  **AI 分析设置 (AI Analysis Settings)**
        *   **图标：** LLM 或抽象的AI智能图标。
        *   **描述：** 配置AI分析服务地址、模型及相关参数。
        *   **作用：** 专注于AI核心功能的配置，不涉及数据操作。

    3.  **数据与分析管理 (Data & Analysis Management)**
        *   **图标：** 数据流/图表与齿轮的组合图标。
        *   **描述：** 管理应用数据、分析过程及结果。
        *   **作用：** 集中处理所有与数据生命周期、分析任务触发和结果管理相关的操作。

    4.  **系统与维护 (System & Maintenance)**
        *   **图标：** 工具箱 或 系统齿轮图标。
        *   **描述：** 查看系统信息，执行系统级维护操作。
        *   **作用：** 提供系统状态的透明度，并进行必要的系统级维护。

---

#### **二、子页面详细结构与逻辑**

##### **1. 文件与导入 (Files & Import) 子页面**

*   **页面标题：** 文件与导入
*   **页面描述：** 管理照片的导入来源，包括监控文件夹和手动上传。
*   **内部模块（按功能逻辑划分）：**

    *   **1.1 扫描文件夹路径**
        *   **描述：** 配置系统自动扫描和监控照片的本地文件夹。当这些文件夹中的照片有变化时，系统将自动识别并进行分析。
        *   **操作元素：**
            *   **文件夹路径输入框：** 用户手动输入完整路径。
            *   **“浏览文件夹”按钮：** 弹出文件选择对话框，方便用户选取文件夹。
            *   **“添加路径”按钮：** 将输入的路径添加到监控列表中。
            *   **“清空输入”按钮：** 清除当前输入框中的内容。

    *   **1.2 手动上传照片**
        *   **描述：** 直接选择并上传照片文件到系统进行分析，适用于少量照片或不希望通过文件夹监控的照片。
        *   **操作元素：**
            *   **“选择照片文件”按钮：** 点击后弹出文件选择器，支持多选。
            *   **(可选) 拖拽上传区域：** 提供一个视觉区域，用户可以直接将文件拖拽至此进行上传。
            *   **提示信息：** “支持多选，上传后照片将自动添加到分析队列中，并存储在系统默认的上传目录中，方便统一管理。”（明确告知上传文件的去向）。

    *   **1.3 已添加的照片来源**
        *   **描述：** 展示所有已添加到系统的照片来源列表，包括通过扫描路径添加的文件夹和通过上传功能导入的照片（若统一存储在特定目录）。
        *   **显示内容：**
            *   一个可滚动的列表，每项包含：
                *   **来源路径/名称：** 例如 `C:\Photos\Family` (扫描)、`系统上传目录` (上传)。
                *   **来源类型：** 明确标识是“扫描路径”还是“上传目录”。
                *   **当前状态：** 如“已分析”、“待分析”、“N张照片”等。
            *   **操作：** 每条扫描路径旁有“删除”按钮，允许用户移除该监控路径。对于上传目录，可能提供“查看”或“清空上传文件”的选项（如果需要）。
        *   **空状态提示：** “暂无照片来源，请通过上方功能添加文件夹或上传照片。”

##### **2. AI 分析设置 (AI Analysis Settings) 子页面**

*   **页面标题：** AI 分析设置
*   **页面描述：** 配置本地或远程大语言模型服务，用于照片数据分析。
*   **内部模块：**

    *   **2.1 AI 服务配置**
        *   **API 地址：** 输入AI服务接口地址。
        *   **模型名称：** 输入使用的模型名称。
        *   **启用AI分析：** 复选框，勾选后启用智能分析功能。
        *   **功能按钮：**
            *   **“保存设置”：** 保存当前配置。
            *   **“测试连接”：** 验证AI服务是否可达。

##### **3. 数据与分析管理 (Data & Analysis Management) 子页面**

*   **页面标题：** 数据与分析管理
*   **页面描述：** 管理应用数据、核心分析任务的触发、状态查看及分析结果的维护。
*   **内部模块：**

    *   **3.1 数据导入/导出**
        *   **导出设置：** 导出当前所有配置和路径设置到文件。
        *   **导入设置：** 从文件导入配置和路径设置。

    *   **3.2 分析过程控制**
        *   **启动内容分析：** 手动触发对所有已添加照片的智能分析。
        *   **检查分析状态：** 查看当前分析任务的进度和状态。
        *   **生成分析报告：** 基于已完成的分析数据生成报告。
        *   **重新分析所有数据：** 强制系统对所有照片重新进行AI分析。
        *   **清除所有分析结果：** 删除所有已保存的AI分析结果（照片文件本身不会被删除）。
        *   **提示信息：** 针对“重新分析”和“清除分析结果”提供清晰的警告和说明。

##### **4. 系统与维护 (System & Maintenance) 子页面**

*   **页面标题：** 系统与维护
*   **页面描述：** 查看系统关键信息，并执行系统级别的维护操作。
*   **内部模块：**

    *   **4.1 系统信息**
        *   **应用版本：** 显示当前应用程序的版本号。
        *   **支持格式：** 列出系统支持的照片文件格式。
        *   **配置文件：** 显示配置文件路径。
        *   **数据目录：** 显示数据存储目录路径。

    *   **4.2 系统维护**
        *   **刷新系统：** 刷新应用程序状态。
        *   **清除缓存：** 清理系统缓存数据。
        *   **重置设置：** 将所有设置恢复到默认状态（需提供明确警告）。
        *   **注意提示：** 针对“重置设置”等破坏性操作，提供显著的警告信息。

---
