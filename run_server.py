#!/usr/bin/env python3
"""
照片分析器 Web 应用启动脚本
支持GUI模式和服务器模式
支持多线程异步AI分析
"""

import uvicorn
import os
import platform
import sys
import webbrowser
import threading
import time
import argparse
from app import app
from modules.utils import Colors
from modules.async_tasks import init_task_worker

def print_banner():
    """打印美化的启动横幅"""
    banner = f"""
{Colors.BLUE}{Colors.BOLD}┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃                                                 ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃  {Colors.GREEN}📊 Photo Analyzer - 照片分析工具{Colors.BLUE}             ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃                                                 ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃  {Colors.YELLOW}版本: 1.0.0{Colors.BLUE}                                ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃  {Colors.YELLOW}日期: 2025年7月{Colors.BLUE}                            ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃                                                 ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛{Colors.END}
    """
    print(banner)

def print_server_info():
    """打印服务器信息"""
    print(f"{Colors.BLUE}{Colors.BOLD}╔════════════════════════════════════════════════╗{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.GREEN}照片分析器服务器模式{Colors.BLUE}                        ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}╠════════════════════════════════════════════════╣{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}• 访问地址: http://127.0.0.1:8000{Colors.BLUE}           ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}• 按 Ctrl+C 停止服务器{Colors.BLUE}                      ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}• 系统: {platform.system()} {platform.release()}{' ' * (33 - len(platform.system()) - len(platform.release()))}{Colors.BLUE}║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}╚════════════════════════════════════════════════╝{Colors.END}")

def open_browser():
    """在浏览器中打开应用"""
    time.sleep(1.5)  # 等待服务器完全启动
    url = 'http://127.0.0.1:8000'
    webbrowser.open(url)
    print(f"{Colors.GREEN}✓ 已在默认浏览器中打开应用界面: {url}{Colors.END}")

def check_environment():
    """检查运行环境"""
    print(f"{Colors.YELLOW}⚙️  环境检查中...{Colors.END}")
    
    # 检查操作系统
    os_name = platform.system()
    os_version = platform.version()
    print(f"  • 操作系统: {os_name} {os_version}")
    
    # 检查Python版本
    py_version = platform.python_version()
    print(f"  • Python版本: {py_version}")
    
    # 检查数据目录
    if not os.path.exists('./data'):
        print(f"{Colors.YELLOW}  • 数据目录不存在，正在创建...{Colors.END}")
        os.makedirs('./data', exist_ok=True)
        print(f"{Colors.GREEN}    ✓ 数据目录已创建{Colors.END}")
    else:
        print(f"{Colors.GREEN}  • 数据目录已存在{Colors.END}")
    
    # 检查配置文件
    if os.path.exists('./config/config.json'):
        print(f"{Colors.GREEN}  • 配置文件已存在{Colors.END}")
    else:
        print(f"{Colors.YELLOW}  • 配置文件不存在，将在首次运行时创建{Colors.END}")
    
    print(f"{Colors.GREEN}✓ 环境检查完成{Colors.END}")

def setup_directories():
    """设置必要的目录"""
    # 确保数据目录存在
    if not os.path.exists('./data'):
        print(f"{Colors.YELLOW}创建数据目录...{Colors.END}")
        os.makedirs('./data', exist_ok=True)
    
    # 确保日志目录存在
    log_dir = os.path.join('./logs')
    if not os.path.exists(log_dir):
        print(f"{Colors.YELLOW}创建日志目录...{Colors.END}")
        os.makedirs(log_dir, exist_ok=True)

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='照片分析器启动脚本')
    parser.add_argument('--gui', action='store_true', help='启用GUI模式（自动打开浏览器）')
    parser.add_argument('--server', action='store_true', help='启用服务器模式（不打开浏览器）')
    args = parser.parse_args()
    
    # 如果没有指定模式，默认为服务器模式
    gui_mode = args.gui and not args.server
    
    # 清屏（跨平台）
    os.system('cls' if os.name == 'nt' else 'clear')
    
    if gui_mode:
        # GUI模式 - 显示美化横幅和环境检查
        print_banner()
        check_environment()
        print(f"\n{Colors.BLUE}🚀 正在启动照片分析器...{Colors.END}")
        
        # 自动打开浏览器
        threading.Thread(target=open_browser).start()
    else:
        # 服务器模式 - 显示服务器信息
        print_server_info()
    
    # 设置必要目录
    setup_directories()
    
    # 启动服务器
    try:
        # 初始化异步任务处理器
        print(f"\n{Colors.GREEN}初始化异步任务处理器...{Colors.END}")
        init_task_worker()
        
        # 检查新增照片
        print(f"\n{Colors.BLUE}检查新增照片...{Colors.END}")
        try:
            from modules.new_photo_detector import NewPhotoDetector
            detector = NewPhotoDetector()
            success, message = detector.check_and_process_new_photos()
            
            if success:
                print(f"{Colors.GREEN}✓ {message}{Colors.END}")
            else:
                print(f"{Colors.YELLOW}⚠ {message}{Colors.END}")
        except Exception as e:
            print(f"{Colors.YELLOW}⚠ 新增照片检测失败: {str(e)}{Colors.END}")
        
        # 启动内容分析检查
        print(f"\n{Colors.BLUE}检查内容分析状态...{Colors.END}")
        try:
            from modules.content_auto_checker import ContentAutoChecker, check_and_auto_analyze
            
            # 获取分析状态
            checker = ContentAutoChecker()
            has_unanalyzed, status = checker.check_unanalyzed_photos()
            
            print(f"{Colors.BLUE}内容分析状态:{Colors.END}")
            print(f"  总照片数: {Colors.YELLOW}{status['total']}{Colors.END}")
            print(f"  已分析: {Colors.GREEN}{status['analyzed']}{Colors.END}")
            print(f"  待分析: {Colors.RED}{status['unanalyzed']}{Colors.END}")
            
            if has_unanalyzed:
                print(f"{Colors.BLUE}发现 {status['unanalyzed']} 张未分析照片，启动后台分析...{Colors.END}")
                
                # 在后台线程中启动内容分析
                import threading
                def background_analysis():
                    try:
                        # 移除数量限制，分析所有未分析的照片
                        analyzed, report = check_and_auto_analyze(max_analyze=None)
                        if analyzed:
                            print(f"{Colors.GREEN}✓ 内容分析完成{Colors.END}")
                        else:
                            print(f"{Colors.YELLOW}⚠ 所有照片都已分析完成{Colors.END}")
                    except Exception as e:
                        print(f"{Colors.RED}✗ 内容分析失败: {str(e)}{Colors.END}")
                
                analysis_thread = threading.Thread(target=background_analysis, daemon=True)
                analysis_thread.start()
                print(f"{Colors.GREEN}✓ 后台内容分析已启动{Colors.END}")
            else:
                print(f"{Colors.GREEN}✓ 所有照片都已分析完成{Colors.END}")
                
        except Exception as e:
            print(f"{Colors.YELLOW}⚠ 内容分析检查失败: {str(e)}{Colors.END}")
        
        print(f"\n{Colors.GREEN}服务器启动中（已禁用动画，提高性能）...{Colors.END}")
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=False,
            access_log=False,
            workers=1,  # 使用单进程模式，避免多进程导致任务队列复制
            lifespan="on",
            timeout_keep_alive=5  # 更快地关闭空闲连接，提高性能
        )
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}服务器已关闭{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}错误: {str(e)}{Colors.END}")
        sys.exit(1)
