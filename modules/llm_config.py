"""
LLM配置处理模块，用于管理LLM API设置
"""

import json
import os

CONFIG_FILE = "./config/config.json"

def get_llm_config():
    """
    获取LLM配置信息
    
    Returns:
        dict: 包含LLM配置的字典，默认为空字典
    """
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("llm", {
                    "api_url": "http://localhost:11434",
                    "model": "gemma3:4b",
                    "enabled": False
                })
        except Exception as e:
            print(f"读取LLM配置时出错: {str(e)}")
            return {
                "api_url": "http://localhost:11434",
                "model": "gemma3:4b",
                "enabled": False
            }
    return {
        "api_url": "http://localhost:11434",
        "model": "gemma3:4b",
        "enabled": False
    }

def save_llm_config(llm_config):
    """
    保存LLM配置信息
    
    Args:
        llm_config (dict): LLM配置字典
    
    Returns:
        bool: 保存成功返回True，否则返回False
    """
    try:
        # 读取现有配置
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {"paths": []}
        
        # 更新LLM配置
        config["llm"] = llm_config
        
        # 保存回文件
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"保存LLM配置时出错: {str(e)}")
        return False
