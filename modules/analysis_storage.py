"""
分析结果存储模块
用于保存和加载LLM分析结果
"""

import json
import os
import time
import hashlib
from typing import Dict, Any, Optional

# 分析结果文件路径
ANALYSIS_FILE = "./data/analysis_results.json"
# CSV文件路径
BASIC_ANALYSIS_CSV = "./data/basic_analysis.csv"

def get_analysis_results() -> Dict[str, Any]:
    """
    获取保存的分析结果
    
    Returns:
        Dict: 包含分析结果的字典，如果不存在则返回空字典
    """
    if os.path.exists(ANALYSIS_FILE):
        try:
            with open(ANALYSIS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取分析结果时出错: {str(e)}")
            return {}
    return {}

def save_analysis_result(chart_type: str, analysis: str, structured_data: Dict[str, Any] = None) -> bool:
    """
    保存分析结果
    
    Args:
        chart_type: 图表类型
        analysis: 分析结果文本
        structured_data: 结构化分析数据(可选)
        
    Returns:
        bool: 保存成功返回True，否则返回False
    """
    try:
        # 确保数据目录存在
        os.makedirs("./data", exist_ok=True)
        
        # 读取现有分析结果
        results = get_analysis_results()
        
        # 获取当前基础分析CSV文件的修改时间和内容哈希
        csv_modified_time = get_csv_last_modified_time()
        csv_content_hash = get_csv_content_hash()
        photos_count = get_photos_count_from_csv()
        
        # 更新分析结果
        results[chart_type] = {
            "analysis": analysis,
            "timestamp": int(time.time()),
            "csv_modified": csv_modified_time,
            "csv_content_hash": csv_content_hash,
            "photos_count": photos_count,
            "structured": structured_data
        }
        
        # 保存到文件
        with open(ANALYSIS_FILE, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 保存分析结果: {chart_type} (照片数: {photos_count}, 哈希: {csv_content_hash[:8]}...)")
        return True
    except Exception as e:
        print(f"保存分析结果时出错: {str(e)}")
        return False

def get_analysis_for_chart(chart_type: str) -> Optional[str]:
    """
    获取指定图表类型的分析结果
    
    Args:
        chart_type: 图表类型
        
    Returns:
        Optional[str]: 分析结果文本，如果不存在则返回None
    """
    results = get_analysis_results()
    if chart_type in results:
        return results[chart_type]["analysis"]
    return None

def is_analysis_outdated(chart_type: str, data_timestamp: int = 0) -> bool:
    """
    检查分析结果是否过期
    只有在CSV文件内容或照片数量发生实质性变化时才需要重新分析
    
    Args:
        chart_type: 图表类型
        data_timestamp: 数据时间戳(可选，不再用于判断过期)
        
    Returns:
        bool: 如果分析结果不存在或数据内容有实质性变化，则返回True
    """
    results = get_analysis_results()
    if chart_type not in results:
        print(f"📋 {chart_type} 无分析结果，需要分析")
        return True
    
    result_data = results[chart_type]
    
    # 检查CSV文件内容是否有变化
    current_csv_hash = get_csv_content_hash()
    saved_csv_hash = result_data.get("csv_content_hash", "")
    
    if current_csv_hash != saved_csv_hash:
        print(f"📋 {chart_type} CSV文件内容已变化，需要重新分析")
        print(f"    当前哈希: {current_csv_hash[:8]}...")
        print(f"    保存哈希: {saved_csv_hash[:8]}...")
        return True
    
    # 检查照片数量是否有变化
    current_photos_count = get_photos_count_from_csv()
    saved_photos_count = result_data.get("photos_count", 0)
    
    if current_photos_count != saved_photos_count:
        print(f"📋 {chart_type} 照片数量已变化，需要重新分析")
        print(f"    当前数量: {current_photos_count}")
        print(f"    保存数量: {saved_photos_count}")
        return True
    
    print(f"📋 {chart_type} 分析结果有效，无需重新分析")
    print(f"    照片数量: {current_photos_count}")
    print(f"    内容哈希: {current_csv_hash[:8]}...")
    return False

def clear_all_analysis() -> bool:
    """
    清除所有分析结果
    
    Returns:
        bool: 操作成功返回True，否则返回False
    """
    try:
        if os.path.exists(ANALYSIS_FILE):
            os.remove(ANALYSIS_FILE)
        return True
    except Exception as e:
        print(f"清除分析结果时出错: {str(e)}")
        return False

def get_csv_last_modified_time() -> int:
    """
    获取基础分析CSV文件的最后修改时间
    
    Returns:
        int: 文件最后修改时间的时间戳，如果文件不存在则返回0
    """
    if os.path.exists(BASIC_ANALYSIS_CSV):
        return int(os.path.getmtime(BASIC_ANALYSIS_CSV))
    return 0

def get_csv_content_hash() -> str:
    """
    获取基础分析CSV文件的内容哈希值
    
    Returns:
        str: 文件内容的MD5哈希值，如果文件不存在则返回空字符串
    """
    if not os.path.exists(BASIC_ANALYSIS_CSV):
        return ""
    
    try:
        with open(BASIC_ANALYSIS_CSV, 'rb') as f:
            content = f.read()
            return hashlib.md5(content).hexdigest()
    except Exception as e:
        print(f"计算CSV文件哈希值时出错: {str(e)}")
        return ""

def get_photos_count_from_csv() -> int:
    """
    从CSV文件中获取照片总数
    
    Returns:
        int: 照片总数，如果获取失败则返回0
    """
    if not os.path.exists(BASIC_ANALYSIS_CSV):
        return 0
    
    try:
        import csv
        with open(BASIC_ANALYSIS_CSV, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if len(row) >= 3 and row[0] == '总体统计' and row[1] == '总照片数量':
                    try:
                        return int(float(row[2]))
                    except (ValueError, TypeError):
                        return 0
    except Exception as e:
        print(f"读取CSV文件照片数量时出错: {str(e)}")
    
    return 0
