#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量照片分析器
用于批量读取目录中的照片EXIF信息并保存到CSV文件
"""

import os
import csv
import sys
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
from datetime import datetime
import glob
from typing import List, Dict, Any, Optional


class BatchPhotoAnalyzer:
    """批量照片分析器类"""
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.tiff', '.tif', '.png']
        # CSV文件的列标题
        self.csv_headers = [
            '文件名', '路径', '拍摄设备', '镜头', '光圈', '快门', 'ISO', 
            '等效35mm焦距', '位置', '拍摄日期', '时间', '图像尺寸', 
            '文件大小', '经度', '纬度', '海拔'
        ]
        
    def _safe_str(self, value):
        """安全地将值转换为字符串"""
        try:
            if hasattr(value, 'numerator') and hasattr(value, 'denominator'):
                if value.denominator == 1:
                    return str(value.numerator)
                else:
                    return f"{value.numerator}/{value.denominator}"
            elif hasattr(value, 'num') and hasattr(value, 'den'):
                if value.den == 1:
                    return str(value.num)
                elif value.den == 0:
                    return str(value.num)
                else:
                    return f"{value.num}/{value.den}"
            elif isinstance(value, bytes):
                try:
                    decoded = value.decode('utf-8', errors='ignore').strip('\x00')
                    if not decoded or not decoded.isprintable():
                        return ""
                    return decoded
                except:
                    return ""
            else:
                result = str(value)
                result = result.replace('\x00', '').strip()
                return result
        except:
            return ""
    
    def _safe_float(self, value):
        """安全地将值转换为浮点数"""
        try:
            if hasattr(value, 'numerator') and hasattr(value, 'denominator'):
                if value.denominator == 0:
                    return 0.0
                return float(value.numerator) / float(value.denominator)
            elif hasattr(value, 'num') and hasattr(value, 'den'):
                if value.den == 0:
                    return float(value.num) if value.num else 0.0
                return float(value.num) / float(value.den)
            else:
                return float(value)
        except:
            return 0.0
    
    def _convert_gps_coord(self, coord):
        """转换GPS坐标格式"""
        try:
            if hasattr(coord, '__iter__') and len(coord) == 3:
                degrees = self._safe_float(coord[0])
                minutes = self._safe_float(coord[1])
                seconds = self._safe_float(coord[2])
                return f"{degrees}°{minutes:.2f}'{seconds:.2f}\""
            return self._safe_str(coord)
        except:
            return ""
    
    def _parse_gps_info(self, gps_data):
        """解析GPS信息"""
        gps_info = {
            'latitude': '',
            'longitude': '',
            'altitude': '',
            'location': ''
        }
        
        lat_coord = None
        lon_coord = None
        lat_ref = None
        lon_ref = None
        
        for tag_id, value in gps_data.items():
            tag = GPSTAGS.get(tag_id, tag_id)
            
            if tag == 'GPSLatitude':
                lat_coord = self._convert_gps_coord(value)
            elif tag == 'GPSLongitude':
                lon_coord = self._convert_gps_coord(value)
            elif tag == 'GPSLatitudeRef':
                lat_ref = self._safe_str(value)
            elif tag == 'GPSLongitudeRef':
                lon_ref = self._safe_str(value)
            elif tag == 'GPSAltitude':
                altitude = self._safe_float(value)
                gps_info['altitude'] = f"{altitude:.2f}m"
        
        # 组合经纬度信息
        if lat_coord and lon_coord:
            gps_info['latitude'] = f"{lat_coord}{lat_ref}" if lat_ref else lat_coord
            gps_info['longitude'] = f"{lon_coord}{lon_ref}" if lon_ref else lon_coord
            gps_info['location'] = f"{gps_info['latitude']}, {gps_info['longitude']}"
        
        return gps_info
    
    def _extract_photo_info(self, image_path: str) -> Dict[str, str]:
        """提取单张照片的信息"""
        result = {header: '' for header in self.csv_headers}
        
        if not os.path.exists(image_path):
            return result
        
        try:
            # 基本文件信息
            result['文件名'] = os.path.basename(image_path)
            result['路径'] = image_path
            result['文件大小'] = f"{os.path.getsize(image_path) / 1024:.2f} KB"
            
            with Image.open(image_path) as image:
                # 图像尺寸
                result['图像尺寸'] = f"{image.width} x {image.height}"
                
                # 获取EXIF信息
                if hasattr(image, '_getexif'):
                    exif_data = image._getexif()
                    if exif_data is not None:
                        self._parse_exif_data(exif_data, result)
                        
        except Exception as e:
            print(f"处理照片 {image_path} 时出错: {str(e)}")
            
        return result
    
    def _parse_exif_data(self, exif_data: Dict[int, Any], result: Dict[str, str]):
        """解析EXIF数据并填充结果字典"""
        for tag_id, value in exif_data.items():
            tag = TAGS.get(tag_id, tag_id)
            
            if tag == 'Make':
                make = self._safe_str(value)
                result['拍摄设备'] = make
            elif tag == 'Model':
                model = self._safe_str(value)
                if result['拍摄设备']:
                    result['拍摄设备'] += f" {model}"
                else:
                    result['拍摄设备'] = model
            elif tag == 'LensModel':
                result['镜头'] = self._safe_str(value)
            elif tag == 'FNumber':
                f_number = self._safe_float(value)
                result['光圈'] = f"f/{f_number:.1f}"
            elif tag == 'ExposureTime':
                exposure_time = self._safe_float(value)
                if exposure_time > 0:
                    if exposure_time < 1:
                        result['快门'] = f"1/{int(1/exposure_time)}s"
                    else:
                        result['快门'] = f"{exposure_time:.3f}s"
            elif tag == 'ISOSpeedRatings':
                result['ISO'] = self._safe_str(value)
            elif tag == 'FocalLength':
                focal_length = self._safe_float(value)
                result['等效35mm焦距'] = f"{focal_length:.1f}mm"
            elif tag == 'FocalLengthIn35mmFilm':
                # 如果有35mm等效焦距，使用这个值
                focal_length_35 = self._safe_float(value)
                result['等效35mm焦距'] = f"{focal_length_35:.1f}mm"
            elif tag == 'DateTimeOriginal' or tag == 'DateTime':
                datetime_str = self._safe_str(value)
                if datetime_str:
                    try:
                        # 解析日期时间: "2025:05:02 11:33:04"
                        dt = datetime.strptime(datetime_str, '%Y:%m:%d %H:%M:%S')
                        result['拍摄日期'] = dt.strftime('%Y-%m-%d')
                        result['时间'] = dt.strftime('%H:%M:%S')
                    except:
                        # 如果解析失败，直接使用原始字符串
                        if ' ' in datetime_str:
                            date_part, time_part = datetime_str.split(' ', 1)
                            result['拍摄日期'] = date_part.replace(':', '-')
                            result['时间'] = time_part
                        else:
                            result['拍摄日期'] = datetime_str
            elif tag == 'GPSInfo':
                gps_info = self._parse_gps_info(value)
                result['位置'] = gps_info.get('location', '')
                result['经度'] = gps_info.get('longitude', '')
                result['纬度'] = gps_info.get('latitude', '')
                result['海拔'] = gps_info.get('altitude', '')
    
    def _find_image_files(self, directory: str) -> List[str]:
        """在目录中查找支持的图像文件"""
        image_files = []
        
        if not os.path.exists(directory):
            print(f"目录不存在: {directory}")
            return image_files
        
        if not os.path.isdir(directory):
            print(f"不是目录: {directory}")
            return image_files
        
        # 递归查找所有支持的图像文件
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_lower = file.lower()
                if any(file_lower.endswith(ext) for ext in self.supported_formats):
                    image_files.append(os.path.join(root, file))
        
        return sorted(image_files)
    
    def process_directory(self, directory: str, output_file: str):
        """处理目录中的所有照片"""
        print(f"正在扫描目录: {directory}")
        
        image_files = self._find_image_files(directory)
        
        if not image_files:
            print(f"在目录 {directory} 中没有找到支持的图像文件")
            return
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        # 检查输出文件是否存在，如果存在则追加模式
        file_exists = os.path.exists(output_file)
        
        with open(output_file, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.csv_headers)
            
            # 如果文件不存在，写入表头
            if not file_exists:
                writer.writeheader()
            
            # 处理每个图像文件
            for i, image_path in enumerate(image_files, 1):
                print(f"正在处理 ({i}/{len(image_files)}): {os.path.basename(image_path)}")
                
                photo_info = self._extract_photo_info(image_path)
                writer.writerow(photo_info)
        
        print(f"处理完成！数据已保存到: {output_file}")
    
    def process_single_file(self, file_path: str, output_file: str):
        """处理单个照片文件"""
        print(f"正在处理文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        # 检查文件格式
        file_lower = file_path.lower()
        if not any(file_lower.endswith(ext) for ext in self.supported_formats):
            print(f"不支持的文件格式: {file_path}")
            return False
        
        try:
            # 检查输出文件是否存在
            file_exists = os.path.exists(output_file)
            
            with open(output_file, 'a', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.csv_headers)
                
                # 如果文件不存在，写入表头
                if not file_exists:
                    writer.writeheader()
                
                # 处理图像文件
                photo_info = self._extract_photo_info(file_path)
                writer.writerow(photo_info)
            
            print(f"处理完成！数据已保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"处理文件时发生错误: {str(e)}")
            return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python batch_photo_analyzer.py <目录路径> [输出文件]")
        print("\n示例:")
        print("python batch_photo_analyzer.py ./photos")
        print("python batch_photo_analyzer.py ./photos output.csv")
        return
    
    directory = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "photos_data.csv"
    
    analyzer = BatchPhotoAnalyzer()
    analyzer.process_directory(directory, output_file)


if __name__ == "__main__":
    main()
