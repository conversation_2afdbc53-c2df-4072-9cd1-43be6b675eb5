#!/usr/bin/env python3
"""
共享工具模块
包含颜色类和其他实用工具
"""

# ANSI 颜色码
class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_colored(text, color=None, bold=False):
    """打印带颜色的文本"""
    if color:
        text = f"{color}{text}{Colors.END}"
    if bold:
        text = f"{Colors.BOLD}{text}{Colors.END}"
    print(text)
