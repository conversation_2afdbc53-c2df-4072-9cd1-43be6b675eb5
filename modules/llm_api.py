import requests
import json
import base64
import os
from typing import Optional, Dict, Any, Union, List
from PIL import Image
import io
import re
from modules.llm_logger import LLMCallLogger  # 导入LLM日志记录器


class OllamaLLMAPI:
    """
    与本地 Ollama 的 Gemma3:4b 模型交互的API类
    支持文字和图片输入
    """
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "gemma3:4b"):
        """
        初始化 Ollama API 客户端
        
        Args:
            base_url: Ollama 服务器地址
            model: 使用的模型名称
        """
        self.base_url = base_url
        self.model = model
        self.api_url = f"{base_url}/api/generate"
        self.chat_url = f"{base_url}/api/chat"
    
    def _compress_image(self, image: Image.Image, max_size: int = 1024) -> Image.Image:
        """
        压缩图片到指定尺寸，保持长宽比
        
        Args:
            image: PIL图片对象
            max_size: 最大尺寸（像素）
            
        Returns:
            压缩后的PIL图片对象
        """
        # 获取原始尺寸
        original_width, original_height = image.size
        
        # 如果图片已经小于或等于目标尺寸，直接返回
        if original_width <= max_size and original_height <= max_size:
            return image
        
        # 计算缩放比例，保持长宽比
        scale_ratio = min(max_size / original_width, max_size / original_height)
        new_width = int(original_width * scale_ratio)
        new_height = int(original_height * scale_ratio)
        
        # 压缩图片
        compressed_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 确保是RGB模式（避免RGBA等模式导致的问题）
        if compressed_image.mode != 'RGB':
            compressed_image = compressed_image.convert('RGB')
        
        return compressed_image
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64字符串，自动压缩到1024x1024
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            base64编码的图片字符串
        """
        try:
            # 使用PIL加载图片并压缩
            with Image.open(image_path) as img:
                compressed_img = self._compress_image(img)
                buffer = io.BytesIO()
                compressed_img.save(buffer, format='JPEG', quality=85)
                return base64.b64encode(buffer.getvalue()).decode('utf-8')
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    def encode_image_from_pil(self, pil_image: Image.Image) -> str:
        """
        将PIL图片对象编码为base64字符串，自动压缩到1024x1024
        
        Args:
            pil_image: PIL图片对象
            
        Returns:
            base64编码的图片字符串
        """
        try:
            compressed_img = self._compress_image(pil_image)
            buffer = io.BytesIO()
            compressed_img.save(buffer, format='JPEG', quality=85)
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
        except Exception as e:
            raise Exception(f"PIL图片编码失败: {str(e)}")
    
    def check_model_availability(self) -> bool:
        """
        检查模型是否可用
        
        Returns:
            模型是否可用
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                models = response.json()
                available_models = [model['name'] for model in models.get('models', [])]
                return self.model in available_models
            return False
        except Exception:
            return False
    
    @LLMCallLogger.log_function_call
    def generate_text(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """
        生成纯文本响应
        
        Args:
            prompt: 用户输入的提示
            system_prompt: 系统提示（可选）
            
        Returns:
            模型生成的文本响应
        """
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "options": {
                    "temperature": 1.5,
                    "top_k": 80,  
                    "top_p": 0.96  
                },
                "stream": False
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = requests.post(self.api_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                raise Exception(f"API请求失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"文本生成失败: {str(e)}")
    
    @LLMCallLogger.log_function_call
    def analyze_image_with_text(self, image_path: str, prompt: str, system_prompt: Optional[str] = None) -> str:
        """
        分析图片并结合文字提示生成响应
        
        Args:
            image_path: 图片文件路径
            prompt: 文字提示
            system_prompt: 系统提示（可选）
            
        Returns:
            模型生成的分析结果
        """
        try:
            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")
            
            # 编码图片
            image_base64 = self.encode_image(image_path)
            
            # 构建请求载荷
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [image_base64],
                "options": {
                    "temperature": 1.5,
                    "top_k": 80,  
                    "top_p": 0.96  
                },
                "stream": False
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = requests.post(self.api_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                raise Exception(f"API请求失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"图片分析失败: {str(e)}")
    
    @LLMCallLogger.log_function_call
    def analyze_pil_image_with_text(self, pil_image: Image.Image, prompt: str, system_prompt: Optional[str] = None) -> str:
        """
        分析PIL图片对象并结合文字提示生成响应
        
        Args:
            pil_image: PIL图片对象
            prompt: 文字提示
            system_prompt: 系统提示（可选）
            
        Returns:
            模型生成的分析结果
        """
        try:
            # 编码PIL图片
            image_base64 = self.encode_image_from_pil(pil_image)
            
            # 构建请求载荷
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [image_base64],
                "options": {
                    "temperature": 1.5,
                    "top_k": 80,  
                    "top_p": 0.96  
                },
                "stream": False
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = requests.post(self.api_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                raise Exception(f"API请求失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"PIL图片分析失败: {str(e)}")
    
    @LLMCallLogger.log_function_call
    def chat_with_history(self, messages: list, system_prompt: Optional[str] = None) -> str:
        """
        支持对话历史的聊天功能
        
        Args:
            messages: 对话历史消息列表，格式为 [{"role": "user", "content": "..."}, ...]
            system_prompt: 系统提示（可选）
            
        Returns:
            模型生成的响应
        """
        try:
            payload = {
                "model": self.model,
                "messages": messages,
                "options": {
                    "temperature": 1.5,
                    "top_k": 80,  
                    "top_p": 0.96  
                },
                "stream": False
            }
            
            if system_prompt:
                payload["messages"].insert(0, {"role": "system", "content": system_prompt})
            
            response = requests.post(self.chat_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('message', {}).get('content', '')
            else:
                raise Exception(f"API请求失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"对话失败: {str(e)}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        try:
            response = requests.post(f"{self.base_url}/api/show", json={"name": self.model})
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"获取模型信息失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"获取模型信息失败: {str(e)}")
    
    @LLMCallLogger.log_function_call
    def generate_structured_output(self, prompt: str, schema: Dict[str, Any], system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        生成结构化输出响应
        
        Args:
            prompt: 用户输入的提示
            schema: 期望的输出结构Schema，JSON Schema格式
            system_prompt: 系统提示（可选）
            
        Returns:
            符合schema的结构化数据
        """
        try:
            # 构建包含schema的系统提示
            schema_instruction = self._build_schema_instruction(schema)
            
            full_system_prompt = schema_instruction
            if system_prompt:
                full_system_prompt = f"{system_prompt}\n\n{schema_instruction}"
            
            # 生成文本响应
            response = self.generate_text(prompt, full_system_prompt)
            
            # 解析结构化输出
            return self._parse_structured_response(response, schema)
            
        except Exception as e:
            raise Exception(f"结构化输出生成失败: {str(e)}")
    
    @LLMCallLogger.log_function_call
    def analyze_image_with_structured_output(self, image_path: str, prompt: str, schema: Dict[str, Any], system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        分析图片并生成结构化输出
        
        Args:
            image_path: 图片文件路径
            prompt: 文字提示
            schema: 期望的输出结构Schema
            system_prompt: 系统提示（可选）
            
        Returns:
            符合schema的结构化数据
        """
        try:
            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")
            
            # 构建包含schema的系统提示
            schema_instruction = self._build_schema_instruction(schema)
            
            full_system_prompt = schema_instruction
            if system_prompt:
                full_system_prompt = f"{system_prompt}\n\n{schema_instruction}"
            
            # 编码图片
            image_base64 = self.encode_image(image_path)
            
            # 构建请求载荷
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [image_base64],
                "stream": False,
                "options": {
                    "temperature": 1.5,
                    "top_k": 80,  
                    "top_p": 0.96  
                },
                "system": full_system_prompt
            }
            
            response = requests.post(self.api_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # 解析结构化输出
                return self._parse_structured_response(response_text, schema)
            else:
                raise Exception(f"API请求失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"图片结构化分析失败: {str(e)}")
    
    @LLMCallLogger.log_function_call
    def analyze_pil_image_with_structured_output(self, pil_image: Image.Image, prompt: str, schema: Dict[str, Any], system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        分析PIL图片对象并生成结构化输出
        
        Args:
            pil_image: PIL图片对象
            prompt: 文字提示
            schema: 期望的输出结构Schema
            system_prompt: 系统提示（可选）
            
        Returns:
            符合schema的结构化数据
        """
        try:
            # 构建包含schema的系统提示
            schema_instruction = self._build_schema_instruction(schema)
            
            full_system_prompt = schema_instruction
            if system_prompt:
                full_system_prompt = f"{system_prompt}\n\n{schema_instruction}"
            
            # 编码PIL图片
            image_base64 = self.encode_image_from_pil(pil_image)
            
            # 构建请求载荷
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [image_base64],
                "stream": False,
                "options": {
                    "temperature": 1.5,
                    "top_k": 80,  
                    "top_p": 0.96  
                },
                "system": full_system_prompt
            }
            
            response = requests.post(self.api_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # 解析结构化输出
                return self._parse_structured_response(response_text, schema)
            else:
                raise Exception(f"API请求失败: {response.status_code}, {response.text}")
                
        except Exception as e:
            raise Exception(f"PIL图片结构化分析失败: {str(e)}")
    
    def _build_schema_instruction(self, schema: Dict[str, Any]) -> str:
        """
        根据schema构建指令
        
        Args:
            schema: JSON Schema格式的输出结构
            
        Returns:
            格式化的指令字符串
        """
        schema_json = json.dumps(schema, indent=2, ensure_ascii=False)
        
        instruction = f"""请按照以下JSON Schema格式严格输出结果，不要添加任何其他文字说明：

JSON Schema:
{schema_json}

输出要求：
1. 输出必须是有效的JSON格式
2. 严格按照schema中定义的字段和类型
3. 不要添加schema中未定义的字段
4. 如果某个字段值不确定，请使用null
5. 直接输出JSON，不要使用markdown代码块包装

输出格式示例：
{{"field1": "value1", "field2": "value2"}}"""
        
        return instruction
    
    def _parse_structured_response(self, response_text: str, schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析结构化响应文本
        
        Args:
            response_text: 模型的响应文本
            schema: 期望的输出结构Schema
            
        Returns:
            解析后的结构化数据
        """
        try:
            # 尝试直接解析JSON
            try:
                return json.loads(response_text.strip())
            except json.JSONDecodeError:
                pass
            
            # 尝试提取JSON代码块
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            
            # 尝试提取花括号内的内容
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', response_text)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            
            # 如果都失败了，返回错误信息
            raise Exception(f"无法解析响应为JSON格式: {response_text}")
            
        except Exception as e:
            # 返回一个基于schema的默认结构，填充错误信息
            return self._create_error_response(schema, str(e))
    
    def _create_error_response(self, schema: Dict[str, Any], error_message: str) -> Dict[str, Any]:
        """
        创建错误响应，基于schema结构
        
        Args:
            schema: JSON Schema
            error_message: 错误信息
            
        Returns:
            错误响应字典
        """
        error_response = {"_error": error_message}
        
        # 尝试根据schema创建默认结构
        if "properties" in schema:
            for field_name, field_schema in schema["properties"].items():
                field_type = field_schema.get("type", "string")
                if field_type == "string":
                    error_response[field_name] = ""
                elif field_type == "number" or field_type == "integer":
                    error_response[field_name] = 0
                elif field_type == "boolean":
                    error_response[field_name] = False
                elif field_type == "array":
                    error_response[field_name] = []
                elif field_type == "object":
                    error_response[field_name] = {}
                else:
                    error_response[field_name] = None
        
        return error_response
    
    @LLMCallLogger.log_function_call
    def simple_chat(self, message: str, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        简单聊天方法，返回标准化的响应格式
        
        Args:
            message: 用户消息
            system_prompt: 系统提示（可选）
            
        Returns:
            包含success和content字段的字典
        """
        try:
            response = self.generate_text(message, system_prompt)
            return {
                "success": True,
                "content": response,
                "error": None
            }
        except Exception as e:
            return {
                "success": False,
                "content": "",
                "error": str(e)
            }

# 便捷函数
def create_ollama_client(base_url: str = "http://localhost:11434", model: str = "gemma3:4b") -> OllamaLLMAPI:
    """
    创建Ollama客户端实例
    
    Args:
        base_url: Ollama服务器地址
        model: 模型名称
        
    Returns:
        OllamaLLMAPI实例
    """
    return OllamaLLMAPI(base_url=base_url, model=model)