"""
摄影数据AI分析模块
用于对图表数据进行分析和生成结构化洞察
"""

import json
import os
import re
import time
from typing import Dict, Any, Optional, List, Union

from modules.llm_api import OllamaLLMAPI
from modules.llm_config import get_llm_config
from modules.analysis_storage import (
    get_analysis_results, save_analysis_result,
    get_analysis_for_chart, is_analysis_outdated, clear_all_analysis
)

# 分析结果结构Schema
ANALYSIS_SCHEMA = {
    "type": "object",
    "properties": {
        "habits": {
            "type": "array",
            "description": "用户的摄影习惯分析，客观描述用户的摄影参数选择和偏好",
            "items": {
                "type": "string",
                "description": "一条客观的摄影习惯描述，例如'主要使用长焦段拍摄，集中在106-200mm范围内'，不超过20字"
            },
            "minItems": 3,
            "maxItems": 5
        },
        "suggestions": {
            "type": "array",
            "description": "基于数据的摄影建议，简洁客观",
            "items": {
                "type": "string",
                "description": "一条针对性的摄影技巧或尝试建议，例如'尝试更广角镜头拍摄开阔风景'，不超过20字"
            },
            "minItems": 2,
            "maxItems": 3
        }
    },
    "required": ["habits", "suggestions"]
}

def get_chart_prompt(chart_type: str, data: str) -> str:
    """
    根据图表类型生成提示词
    
    Args:
        chart_type: 图表类型
        data: 图表数据
        
    Returns:
        str: 提示词
    """
    # 尝试加载基础知识文件
    basic_knowledge = ""
    try:
        # 使用绝对路径加载基础知识文件
        basic_knowledge_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "basic_knowledge.json")
        if os.path.exists(basic_knowledge_path):
            with open(basic_knowledge_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    # 确认内容是有效的JSON
                    try:
                        json_content = json.loads(content)
                        basic_knowledge = f"\n\n基础知识参考：\n{json.dumps(json_content, ensure_ascii=False, indent=2)}"
                    except json.JSONDecodeError:
                        print(f"基础知识文件包含无效的JSON格式")
    except Exception as e:
        print(f"读取基础知识文件失败: {str(e)}")
    
    # 简化的提示词模板
    prompts = {
        "focal_lengths": f"""仔细分析以下焦距数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：最常用焦段占比，焦段集中度，题材偏好推测，未使用焦段
建议要点：未充分利用焦段的建议，基于当前焦段的拓展技巧，多样化建议""",
        
        "apertures": f"""仔细分析以下光圈数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：最常用光圈区间，景深偏好，光圈与题材关系，光圈使用多样性
建议要点：景深控制技巧，未使用光圈范围建议，特定题材的光圈推荐""",
        
        "shutter_speeds": f"""仔细分析以下快门速度数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：最常用快门速度，动静态偏好，手持/三脚架使用情况，技术习惯
建议要点：未尝试快门区间建议，稳定性建议，创意技巧推荐""",
        
        "iso_values": f"""仔细分析以下ISO数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：最常用ISO值区间，低光适应性，噪点容忍度，拍摄环境分析
建议要点：ISO优化技巧，与其他参数的协调使用，多样化光线条件尝试""",
        
        "devices": f"""仔细分析以下相机设备数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：主要设备及特点，设备与拍摄风格匹配度，设备优势与局限
建议要点：设备性能最大化技巧，适合的拍摄题材，设备使用场景分配""",
        
        "lenses": f"""仔细分析以下镜头数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：主要镜头及特点，题材偏好，使用集中度，镜头与风格关联
建议要点：发挥镜头潜力的建议，镜头多样化建议，覆盖范围优化""",
        
        "months": f"""仔细分析以下月份拍摄数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：拍摄集中月份，季节规律，可能的题材偏好，空缺时间段
建议要点：空缺月份拍摄建议，季节光线利用，主题拓展方向""",
        
        "time_periods": f"""仔细分析以下时段拍摄数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：主要拍摄时段，光线偏好，题材和生活习惯推测，未尝试时段
建议要点：时段拓展建议，光线利用技巧，特定时段题材推荐"""
    }
    
    # 默认提示词
    default_prompt = f"""仔细分析以下摄影数据，认真判断每项数据的数量，输出简短摄影习惯分析和建议：

{data}{basic_knowledge}

分析要点：数据模式和偏好，参数使用频率与集中度，拍摄习惯和题材推断，未充分利用的区域
建议要点：多样化建议，技术拓展方向，适合尝试的新参数组合"""
    
    return prompts.get(chart_type, default_prompt)

def get_system_prompt() -> str:
    """
    获取系统提示词
    
    Returns:
        str: 系统提示词
    """
    return """你是专业摄影数据分析系统，分析用户拍摄数据提供简短摄影习惯描述和建议。

分析要求:
- 客观、数据驱动，不使用第一/二人称
- 每条描述控制在50字以内
- 建议可更加详细
- 直接陈述事实和模式，不加主观评价
- 建议具体可操作，与数据有明确关联

输出格式:
- habits数组：3-5条客观拍摄习惯描述
- suggestions数组：2-3条具体建议

直接输出JSON格式结果，遵循以下格式，不要添加额外解释。
{
  "habits": ["习惯1", "习惯2", "习惯3"],
  "suggestions": ["建议1", "建议2"]
}"""

def analyze_chart_data(chart_type: str, data: str, force_new: bool = False) -> Dict[str, Any]:
    """
    使用LLM分析图表数据，并返回结构化输出
    
    Args:
        chart_type: 图表类型
        data: 图表数据
        force_new: 是否强制重新分析
    
    Returns:
        Dict: 分析结果
    """
    llm_config = get_llm_config()
    
    # 如果不强制重新分析，先检查是否有缓存的分析结果
    if not force_new:
        # 获取缓存的分析结果
        results = get_analysis_results()
        
        # 检查是否存在该图表类型的分析结果
        if chart_type in results:
            result_data = results[chart_type]
            
            # 检查是否过期（基于CSV内容和照片数量变化）
            if not is_analysis_outdated(chart_type):
                # 优先使用保存的结构化数据
                if "structured" in result_data:
                    print(f"✅ 使用缓存的分析结果: {chart_type}")
                    return {
                        "success": True, 
                        "analysis": result_data.get("analysis", ""), 
                        "structured": result_data["structured"],
                        "cached": True
                    }
                # 如果没有结构化数据，将文本分析结果转换为结构化格式
                elif "analysis" in result_data:
                    print(f"✅ 转换缓存的文本分析结果: {chart_type}")
                    structured_result = text_to_structured_format(result_data["analysis"])
                    return {
                        "success": True, 
                        "analysis": result_data["analysis"], 
                        "structured": structured_result,
                        "cached": True
                    }
    
    # 如果LLM未启用，无法生成新的分析
    if not llm_config.get("enabled", False):
        print(f"❌ LLM服务未启用，无法分析 {chart_type}")
        return {"success": False, "message": "LLM服务未启用"}
    
    print(f"🤖 开始AI分析: {chart_type}")
    
    try:
        # 创建LLM客户端（每次调用都创建新客户端，确保独立对话）
        api = OllamaLLMAPI(base_url=llm_config.get("api_url"), model=llm_config.get("model"))
        
        # 获取提示词
        prompt = get_chart_prompt(chart_type, data)
        system_prompt = get_system_prompt()
        
        # 调用结构化输出API（不传递历史对话）
        try:
            # 尝试生成结构化输出
            print(f"🔍 尝试生成结构化分析...")
            result = api.generate_structured_output(prompt, ANALYSIS_SCHEMA, system_prompt)
            
            # 确保habits至少有一项
            if not result.get("habits") or len(result.get("habits", [])) == 0:
                print(f"⚠️ 结构化分析未返回有效数据，使用文本分析...")
                # 如果没有洞察，使用常规文本生成并转换
                text_result = api.generate_text(prompt, system_prompt)
                result = text_to_structured_format(text_result)
            
            # 将结构化结果转换回文本格式进行存储
            formatted_text = structured_to_text_format(result)
            
            # 保存分析结果，同时存储结构化数据
            save_analysis_result(chart_type, formatted_text, result)
            
            print(f"✅ AI分析完成并保存: {chart_type}")
            return {
                "success": True, 
                "analysis": formatted_text,
                "structured": result
            }
            
        except Exception as e:
            # 如果结构化输出失败，回退到常规文本生成
            print(f"⚠️ 结构化分析失败，回退到文本分析: {str(e)}")
            text_result = api.generate_text(prompt, system_prompt + "\n请以要点形式（使用'• '开头）提供3-4个简短的专业见解，每个要点不超过20字。")
            
            # 格式化文本响应
            formatted_text = format_text_response(text_result)
            
            # 将文本转换为结构化格式
            structured_result = text_to_structured_format(formatted_text)
            
            # 保存分析结果，同时存储结构化数据
            save_analysis_result(chart_type, formatted_text, structured_result)
            
            print(f"✅ AI分析完成并保存: {chart_type}")
            return {
                "success": True, 
                "analysis": formatted_text,
                "structured": structured_result
            }
            
    except Exception as e:
        print(f"❌ LLM调用错误: {str(e)}")
        return {"success": False, "message": f"LLM调用错误: {str(e)}"}

def format_text_response(text: str) -> str:
    """
    格式化LLM响应文本
    
    Args:
        text: LLM响应文本
    
    Returns:
        str: 格式化后的文本
    """
    formatted_text = text.strip()
    
    # 移除多余的前缀
    formatted_text = re.sub(r'^(分析：|结论：|总结：|以下是|要点：|分析结果：)', '', formatted_text)
    
    # 确保每个要点都是以'• '或'- '开头
    if not re.search(r'(^|\n)[•\-] ', formatted_text):
        # 按句子分割，并添加要点符号
        sentences = re.split(r'(?<=[。！？.!?])\s*', formatted_text)
        sentences = [s for s in sentences if s.strip()]
        formatted_text = '\n'.join([f'• {s.strip()}' for s in sentences if s.strip()])
    
    # 标准化要点符号为'• '
    formatted_text = re.sub(r'(^|\n)-\s+', r'\1• ', formatted_text)
    
    return formatted_text.strip()

def text_to_structured_format(text: str) -> Dict[str, Any]:
    """
    将文本格式转换为结构化格式
    
    Args:
        text: 文本格式的分析结果
    
    Returns:
        Dict: 结构化格式
    """
    # 初始化结构
    result = {
        "habits": [],
        "suggestions": []
    }
    
    # 尝试解析JSON格式的文本
    try:
        # 尝试从文本中提取JSON
        json_pattern = r'\{[\s\S]*?\}'
        json_matches = re.findall(json_pattern, text)
        
        for json_text in json_matches:
            try:
                data = json.loads(json_text)
                if isinstance(data, dict):
                    # 如果是JSON格式，提取habits和suggestions字段
                    if "habits" in data and isinstance(data["habits"], list):
                        result["habits"] = data["habits"]
                    if "suggestions" in data and isinstance(data["suggestions"], list):
                        result["suggestions"] = data["suggestions"]
                    
                    # 如果找到了有效数据，直接返回
                    if result["habits"] or result["suggestions"]:
                        return result
            except json.JSONDecodeError:
                continue
                
        # 如果没有找到有效的JSON结构，尝试直接解析整个文本
        try:
            data = json.loads(text)
            if isinstance(data, dict):
                # 如果是JSON格式，提取habits和suggestions字段
                if "habits" in data:
                    result["habits"] = data["habits"]
                if "suggestions" in data:
                    result["suggestions"] = data["suggestions"]
                return result
        except json.JSONDecodeError:
            # 非JSON格式，继续处理文本
            pass
    except:
        # 解析失败，继续处理文本
        pass
        
    # 提取要点
    bullet_points = re.findall(r'[•\-] (.+?)(?=\n[•\-]|\n\n|$)', text, re.DOTALL)
    if not bullet_points:
        # 如果没有找到要点格式，按行分割
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        bullet_points = lines
    
    # 清洗提取的要点
    insights = [point.strip() for point in bullet_points if point.strip()]
    
    if not insights:
        return result
    
    # 根据要点数量分配到不同的类别
    if len(insights) <= 2:
        # 如果只有1-2个要点，全部作为习惯
        result["habits"] = insights
    elif len(insights) == 3:
        # 如果有3个要点，前2个作为习惯，最后1个作为建议
        result["habits"] = insights[:2]
        result["suggestions"] = insights[2:]
    else:
        # 有4个或更多的要点
        total = len(insights)
        habits_count = min(total - 2, 5)  # 至少留2个给suggestions，habits最多5个
        
        result["habits"] = insights[:habits_count]
        result["suggestions"] = insights[habits_count:habits_count+3]  # suggestions最多3个
    
    return result

def structured_to_text_format(data: Dict[str, Any]) -> str:
    """
    将结构化格式转换为文本格式
    
    Args:
        data: 结构化格式的分析结果
    
    Returns:
        str: 文本格式
    """
    # 检查数据的有效性
    if not isinstance(data, dict):
        return ""
    
    text_parts = []
    
    # 添加拍摄习惯
    habits = data.get("habits", [])
    if habits and isinstance(habits, list):
        for habit in habits:
            if habit and isinstance(habit, str) and habit.strip():
                # 移除可能的前缀符号
                habit_text = habit.strip()
                if habit_text.startswith("• ") or habit_text.startswith("- "):
                    habit_text = habit_text[2:].strip()
                text_parts.append(f"• {habit_text}")
    
    # 添加建议
    suggestions = data.get("suggestions", [])
    if suggestions and isinstance(suggestions, list):
        for suggestion in suggestions:
            if suggestion and isinstance(suggestion, str) and suggestion.strip():
                # 移除可能的前缀符号
                suggestion_text = suggestion.strip()
                if suggestion_text.startswith("• ") or suggestion_text.startswith("- "):
                    suggestion_text = suggestion_text[2:].strip()
                
                # 避免重复
                if not any(suggestion_text == part.strip()[2:] for part in text_parts):
                    text_parts.append(f"• {suggestion_text}")
    
    return "\n".join(text_parts)

def batch_analyze_all_charts(force_new: bool = False) -> Dict[str, Any]:
    """
    批量分析所有图表类型的数据
    
    Args:
        force_new: 是否强制重新分析所有图表
    
    Returns:
        Dict: 批量分析结果
    """
    try:
        # 导入chart_data_processor模块
        from modules.chart_data_processor import PhotoDataProcessor
        
        # 创建数据处理器并加载数据
        processor = PhotoDataProcessor("./data/basic_analysis.csv")
        if not processor.load_and_parse_csv():
            return {"success": False, "message": "无法加载CSV数据"}
        
        # 处理数据
        processor.process_data_for_charts()
        
        # 图表类型映射
        chart_type_mapping = {
            'focal_lengths': 'focal_lengths',
            'apertures': 'apertures', 
            'shutter_speeds': 'shutter_speeds',
            'iso_values': 'iso_values',
            'devices': 'devices',
            'lenses': 'lenses',
            'months': 'months',
            'time_periods': 'time_periods'
        }
        
        results = {}
        total_charts = len(chart_type_mapping)
        successful_analyses = 0
        
        print(f"🚀 开始批量分析 {total_charts} 个图表类型...")
        
        for data_key, chart_type in chart_type_mapping.items():
            if data_key in processor.processed_data and processor.processed_data[data_key]:
                # 准备数据（不包含时间戳）
                chart_data = json.dumps({
                    "type": chart_type,
                    "data": processor.processed_data[data_key]
                }, ensure_ascii=False)
                
                # 分析数据
                try:
                    result = analyze_chart_data(chart_type, chart_data, force_new)
                    results[chart_type] = result
                    
                    if result.get("success"):
                        successful_analyses += 1
                        print(f"✅ {chart_type} 分析完成")
                    else:
                        print(f"❌ {chart_type} 分析失败: {result.get('message', '未知错误')}")
                        
                except Exception as e:
                    print(f"❌ {chart_type} 分析异常: {str(e)}")
                    results[chart_type] = {"success": False, "message": str(e)}
            else:
                print(f"⏭️ {chart_type} 无有效数据，跳过分析")
                results[chart_type] = {"success": False, "message": "无有效数据"}
        
        print(f"📊 批量分析完成: {successful_analyses}/{total_charts} 个图表分析成功")
        
        return {
            "success": True,
            "message": f"批量分析完成: {successful_analyses}/{total_charts} 个图表分析成功",
            "results": results,
            "total_charts": total_charts,
            "successful_analyses": successful_analyses
        }
        
    except Exception as e:
        print(f"❌ 批量分析失败: {str(e)}")
        return {"success": False, "message": f"批量分析失败: {str(e)}"}
