#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片信息分析器
用于读取和显示照片的EXIF信息，包括设备、镜头、光圈等元数据
"""

import os
import sys
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import json
from datetime import datetime


class PhotoAnalyzer:
    """照片信息分析器类"""
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.tiff', '.tif']
        # 需要特殊处理的EXIF标签
        self.binary_tags = {
            'MakerNote', 'UserComment', 'ComponentsConfiguration', 'FlashPixVersion', 
            'ExifVersion', 'SceneType', 'FileSource', 'CFAPattern', 'SpatialFrequencyResponse',
            'OECF', 'SpectralSensitivity', 'GPSProcessingMethod', 'GPSAreaInformation'
        }
        # 重要的EXIF标签（优先显示）
        self.important_tags = {
            'Make', 'Model', 'LensModel', 'DateTime', 'DateTimeOriginal', 'DateTimeDigitized',
            'FNumber', 'ExposureTime', 'ISOSpeedRatings', 'FocalLength', 'Flash', 'WhiteBalance',
            'ExposureMode', 'MeteringMode', 'Software', 'Artist', 'Copyright', 'ImageDescription',
            'Orientation', 'ColorSpace', 'ExifImageWidth', 'ExifImageHeight'
        }
    
    def _safe_str(self, value):
        """安全地将值转换为字符串"""
        try:
            if hasattr(value, 'numerator') and hasattr(value, 'denominator'):
                if value.denominator == 1:
                    return str(value.numerator)
                else:
                    return f"{value.numerator}/{value.denominator}"
            elif hasattr(value, 'num') and hasattr(value, 'den'):
                if value.den == 1:
                    return str(value.num)
                elif value.den == 0:
                    return str(value.num)
                else:
                    return f"{value.num}/{value.den}"
            elif isinstance(value, bytes):
                # 处理字节数据
                try:
                    # 尝试解码为UTF-8，忽略错误
                    decoded = value.decode('utf-8', errors='ignore').strip('\x00')
                    # 如果解码后是空字符串或只有控制字符，返回十六进制表示
                    if not decoded or not decoded.isprintable():
                        return f"<binary data: {len(value)} bytes>"
                    return decoded
                except:
                    return f"<binary data: {len(value)} bytes>"
            else:
                result = str(value)
                # 清理字符串中的null字符
                result = result.replace('\x00', '').strip()
                # 检查是否包含过多的不可打印字符
                printable_chars = sum(1 for c in result if c.isprintable())
                if len(result) > 0 and printable_chars / len(result) < 0.7:
                    return f"<mixed data: {len(result)} chars>"
                return result
        except:
            return str(type(value).__name__)
    
    def _safe_float(self, value):
        """安全地将值转换为浮点数"""
        try:
            if hasattr(value, 'numerator') and hasattr(value, 'denominator'):
                return float(value.numerator) / float(value.denominator)
            elif hasattr(value, 'num') and hasattr(value, 'den'):
                if value.den != 0:
                    return float(value.num) / float(value.den)
                else:
                    return float(value.num)
            else:
                return float(value)
        except:
            return 0.0
    
    def read_photo_info(self, image_path):
        """读取照片的所有EXIF信息"""
        if not os.path.exists(image_path):
            return {"error": f"文件不存在: {image_path}"}
        
        try:
            with Image.open(image_path) as image:
                # 获取基本信息
                basic_info = {
                    "文件名": os.path.basename(image_path),
                    "文件路径": image_path,
                    "文件大小": f"{os.path.getsize(image_path) / 1024:.2f} KB",
                    "图像尺寸": f"{image.width} x {image.height}",
                    "颜色模式": image.mode,
                    "格式": image.format
                }
                
                # 获取EXIF信息
                exif_info = {}
                if hasattr(image, '_getexif'):
                    exif_data = image._getexif()
                    if exif_data is not None:
                        exif_info = self._parse_exif_data_clean(exif_data)
                
                return {
                    "基本信息": basic_info,
                    "EXIF信息": exif_info
                }
                
        except Exception as e:
            return {"error": f"读取照片信息时发生错误: {str(e)}"}
    
    def _parse_exif_data_clean(self, exif_data):
        """解析EXIF数据 - 清洁版本"""
        exif_info = {}
        
        for tag_id, value in exif_data.items():
            tag = TAGS.get(tag_id, tag_id)
            
            # 跳过二进制数据标签
            if tag in self.binary_tags:
                continue
            
            # 特殊处理重要标签
            if tag == 'GPSInfo':
                gps_info = self._parse_gps_info_clean(value)
                if gps_info:
                    exif_info['GPS信息'] = gps_info
            elif tag == 'DateTime':
                exif_info['拍摄时间'] = self._safe_str(value)
            elif tag == 'Make':
                exif_info['相机制造商'] = self._safe_str(value)
            elif tag == 'Model':
                exif_info['相机型号'] = self._safe_str(value)
            elif tag == 'LensModel':
                exif_info['镜头型号'] = self._safe_str(value)
            elif tag == 'FNumber':
                f_number = self._safe_float(value)
                exif_info['光圈值'] = f"f/{f_number:.1f}"
            elif tag == 'ExposureTime':
                exposure_time = self._safe_float(value)
                if exposure_time < 1:
                    exif_info['快门速度'] = f"1/{int(1/exposure_time)}s"
                else:
                    exif_info['快门速度'] = f"{exposure_time:.3f}s"
            elif tag == 'ISOSpeedRatings':
                iso_value = self._safe_str(value)
                exif_info['ISO'] = iso_value
            elif tag == 'FocalLength':
                focal_length = self._safe_float(value)
                exif_info['焦距'] = f"{focal_length:.1f}mm"
            elif tag == 'Flash':
                exif_info['闪光灯'] = self._parse_flash_info(value)
            elif tag == 'WhiteBalance':
                exif_info['白平衡'] = '自动' if value == 0 else '手动'
            elif tag == 'ExposureMode':
                exposure_modes = {0: '自动', 1: '手动', 2: '自动包围'}
                exif_info['曝光模式'] = exposure_modes.get(value, f'未知({value})')
            elif tag == 'MeteringMode':
                metering_modes = {
                    0: '未知', 1: '平均', 2: '中央重点', 3: '点测光', 
                    4: '多点', 5: '图案', 6: '部分', 255: '其他'
                }
                exif_info['测光模式'] = metering_modes.get(value, f'未知({value})')
            elif tag == 'Software':
                exif_info['软件'] = self._safe_str(value)
            elif tag == 'Artist':
                exif_info['作者'] = self._safe_str(value)
            elif tag == 'Copyright':
                exif_info['版权'] = self._safe_str(value)
            elif tag == 'ImageDescription':
                exif_info['图像描述'] = self._safe_str(value)
            elif tag == 'Orientation':
                orientations = {
                    1: '正常', 2: '水平翻转', 3: '旋转180°', 4: '垂直翻转',
                    5: '水平翻转+逆时针90°', 6: '顺时针90°', 7: '水平翻转+顺时针90°', 8: '逆时针90°'
                }
                exif_info['方向'] = orientations.get(value, f'未知({value})')
            elif tag == 'ColorSpace':
                color_spaces = {1: 'sRGB', 2: 'Adobe RGB', 65535: '未校准'}
                exif_info['色彩空间'] = color_spaces.get(value, f'未知({value})')
            elif tag in self.important_tags:
                # 其他重要标签
                value_str = self._safe_str(value)
                if len(value_str) <= 100:  # 只显示合理长度的值
                    exif_info[self._safe_str(tag)] = value_str
        
        return exif_info
    
    def _parse_gps_info_clean(self, gps_data):
        """解析GPS信息 - 清洁版本"""
        gps_info = {}
        
        for tag_id, value in gps_data.items():
            tag = GPSTAGS.get(tag_id, tag_id)
            
            if tag == 'GPSLatitude':
                gps_info['纬度'] = self._convert_gps_coord(value)
            elif tag == 'GPSLongitude':
                gps_info['经度'] = self._convert_gps_coord(value)
            elif tag == 'GPSLatitudeRef':
                gps_info['纬度方向'] = self._safe_str(value)
            elif tag == 'GPSLongitudeRef':
                gps_info['经度方向'] = self._safe_str(value)
            elif tag == 'GPSAltitude':
                altitude = self._safe_float(value)
                gps_info['海拔'] = f"{altitude:.2f}m"
            elif tag == 'GPSDateStamp':
                gps_info['GPS日期'] = self._safe_str(value)
            elif tag == 'GPSTimeStamp':
                if hasattr(value, '__iter__') and len(value) == 3:
                    hour = int(self._safe_float(value[0]))
                    minute = int(self._safe_float(value[1]))
                    second = int(self._safe_float(value[2]))
                    gps_info['GPS时间'] = f"{hour:02d}:{minute:02d}:{second:02d}"
                else:
                    gps_info['GPS时间'] = self._safe_str(value)
        
        return gps_info
    
    def _convert_gps_coord(self, coord):
        """转换GPS坐标格式"""
        try:
            if hasattr(coord, '__iter__') and len(coord) == 3:
                degrees = self._safe_float(coord[0])
                minutes = self._safe_float(coord[1])
                seconds = self._safe_float(coord[2])
                return f"{degrees}°{minutes:.2f}'{seconds:.2f}\""
            return self._safe_str(coord)
        except:
            return self._safe_str(coord)
    
    def _parse_flash_info(self, flash_value):
        """解析闪光灯信息"""
        try:
            flash_value = int(flash_value)
            flash_fired = flash_value & 0x01
            flash_mode = (flash_value >> 3) & 0x03
            
            result = []
            if flash_fired:
                result.append("闪光灯闪烁")
            else:
                result.append("闪光灯未闪烁")
            
            if flash_mode == 1:
                result.append("强制闪光")
            elif flash_mode == 2:
                result.append("强制不闪光")
            elif flash_mode == 3:
                result.append("自动模式")
            
            return ", ".join(result)
        except:
            return self._safe_str(flash_value)
    
    def print_photo_info(self, image_path):
        """打印照片信息"""
        info = self.read_photo_info(image_path)
        
        if "error" in info:
            print(f"错误: {info['error']}")
            return
        
        print(f"\n{'='*60}")
        print(f"照片信息分析报告")
        print(f"{'='*60}")
        
        # 打印基本信息
        print(f"\n【基本信息】")
        print(f"-" * 30)
        for key, value in info.get("基本信息", {}).items():
            print(f"{key:<15}: {value}")
        
        # 打印EXIF信息
        exif_info = info.get("EXIF信息", {})
        if exif_info:
            print(f"\n【EXIF信息】")
            print(f"-" * 30)
            
            # 重要信息优先显示
            important_keys = [
                '相机制造商', '相机型号', '镜头型号', '拍摄时间', 
                '光圈值', '快门速度', 'ISO', '焦距', '闪光灯', 
                '白平衡', '曝光模式', '测光模式', '软件', '方向', '色彩空间'
            ]
            
            # 先显示重要信息
            for key in important_keys:
                if key in exif_info:
                    print(f"{key:<15}: {exif_info[key]}")
            
            # 显示GPS信息
            if 'GPS信息' in exif_info:
                gps_info = exif_info['GPS信息']
                if gps_info:
                    print(f"\n【GPS信息】")
                    print(f"-" * 30)
                    for key, value in gps_info.items():
                        print(f"{key:<15}: {value}")
        
        print(f"\n{'='*60}")


def main():
    """主函数"""
    analyzer = PhotoAnalyzer()
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python photo_analyzer_clean.py <照片路径>")
        print("\n示例:")
        print("python photo_analyzer_clean.py example.jpg")
        return
    
    image_path = sys.argv[1]
    analyzer.print_photo_info(image_path)


if __name__ == "__main__":
    main()
