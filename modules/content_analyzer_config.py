#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片内容分析器配置
包含提示词模板和JSON Schema定义
"""

# 图片分析JSON Schema
ANALYSIS_SCHEMA = {
    "type": "object",
    "properties": {
        "score": {
            "type": "integer",
            "minimum": 0,
            "maximum": 100,
            "description": "图片质量评分，0-100分"
        },
        "tone": {
            "type": "string",
            "enum": ["暖色调", "冷色调", "中性色调", "高饱和", "低饱和"],
            "description": "色调分类"
        },
        "tags": {
            "type": "array",
            "items": {
                "type": "string",
                "enum": ["人像", "风景", "建筑", "美食", "动物", "植物", "街拍", "夜景", "运动", "静物", "抽象", "其他"]
            },
            "minItems": 1,
            "maxItems": 3,
            "description": "题材标签列表"
        }
    },
    "required": ["score", "tone", "tags"]
}

# 色调分类说明
TONE_DESCRIPTIONS = {
    "暖色调": "红橙黄为主，温暖热烈",
    "冷色调": "蓝绿紫为主，冷静清新",
    "中性色调": "灰白黑为主，平衡简洁",
    "高饱和": "色彩鲜艳明亮，对比强烈",
    "低饱和": "色彩柔和淡雅，对比温和"
}

# 题材分类说明
TAG_DESCRIPTIONS = {
    "人像": "人物肖像、生活照、艺术人像",
    "风景": "自然风光、城市景观、山水建筑",
    "建筑": "建筑物、室内空间、建筑细节",
    "美食": "食物、饮品、餐具等美食相关",
    "动物": "宠物、野生动物、昆虫等",
    "植物": "花卉、树木、草地等植物题材",
    "街拍": "街头摄影、日常生活、城市人文",
    "夜景": "夜晚拍摄、灯光、星空等",
    "运动": "体育、动态场景、运动主题",
    "静物": "物品、产品、艺术品等静态物体",
    "抽象": "艺术性强、抽象表现、创意构图",
    "其他": "不属于以上分类的其他类型"
}

# 分析提示词模板
ANALYSIS_PROMPT = """作为专业摄影师，请对这张照片进行非常细致的评分和分类。

**评分标准（0-100分）：**
- 90-100分：技术艺术俱佳
- 80-89分：值得收藏
- 70-79分：有亮点
- 60-69分：基本达标
- 50-59分：有改进空间
- 40-49分：技术缺陷明显
- 30-39分：问题多
- 20-29分：不达标
- 10-19分：严重问题
- 0-9分：无法观看

**评分要点：**
1. **技术质量（40%）**：对焦、曝光、色彩、画质
2. **构图艺术（35%）**：构图、平衡、主体突出
3. **创意表达（25%）**：光影、情感、创新性

**评分原则：**
- 首先仔细辨认和理解图片内容
- 仔细观察图片完成之后再进行打分
- 细化评分，避免大致打分
- 评分严苛，要评判图片的方方面面
- 分数不能过高或者过低，要非常客观公正

**色调分类：**
""" + "\n".join([f"- {tone}：{desc}" for tone, desc in TONE_DESCRIPTIONS.items()]) + """

**题材分类（选择1-3个）：**
""" + "\n".join([f"- {tag}：{desc}" for tag, desc in TAG_DESCRIPTIONS.items()]) + """

请严格按照标准评分，确保分数真实反映照片质量！"""

# 默认配置
DEFAULT_CONFIG = {
    "batch_size": 10,
    "delay_between_requests": 0.5,
    "csv_input_path": "data/raw.csv",
    "csv_output_path": "data/advanced_analysis.csv",
    "report_output_path": "data/advanced_analysis_report.json",
    "supported_formats": ["jpg", "jpeg", "png", "tiff", "tif", "mpo", "bmp", "gif", "webp"],
    "default_values": {
        "score": 0,
        "tone": "未知",
        "tags": ["其他"]
    }
}

def get_analysis_config():
    """获取分析配置"""
    return {
        "prompt": ANALYSIS_PROMPT,
        "schema": ANALYSIS_SCHEMA,
        "config": DEFAULT_CONFIG
    }
