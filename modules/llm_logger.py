"""
LLM调用日志模块，记录每次LLM API调用的输入和输出
简化格式，只保留对话内容
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional, Union, List

# 日志文件路径
LOG_DIR = os.path.join("logs")
LLM_LOG_FILE = os.path.join(LOG_DIR, "llm_calls.jsonl")

def ensure_log_dir():
    """确保日志目录存在"""
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR, exist_ok=True)

def log_llm_call(
    function_name: str,
    inputs: Dict[str, Any],
    outputs: Union[str, Dict[str, Any], None],
    error: Optional[str] = None,
    duration_ms: Optional[float] = None
) -> None:
    """
    记录LLM调用，简化格式只保留对话内容
    
    Args:
        function_name: 调用的函数名
        inputs: 输入参数，需要包括提示词等信息
        outputs: LLM返回的输出
        error: 错误信息，如果有
        duration_ms: 调用耗时（毫秒）
    """
    ensure_log_dir()
    
    # 提取主要的对话内容
    prompt = ""
    if "prompt" in inputs:
        prompt = inputs["prompt"]
    elif "self" in inputs:
        prompt = inputs["self"]
    
    # 构造简化的日志条目
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "function": function_name,
        "prompt": prompt,
        "response": outputs,
    }
    
    if error:
        log_entry["error"] = error
    
    # 将日志写入文件
    try:
        with open(LLM_LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"写入LLM日志失败: {str(e)}")

class LLMCallLogger:
    """LLM调用日志记录器，用于包装函数调用并记录"""
    
    @staticmethod
    def log_function_call(func):
        """装饰器：记录函数调用的输入和输出"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = func.__name__
            
            # 准备记录输入参数（处理args）
            inputs = {}
            
            # 处理实例方法的self参数
            if len(args) > 0 and hasattr(args[0], '__class__') and args[0].__class__.__name__ == 'OllamaLLMAPI':
                # 如果是OllamaLLMAPI实例方法，记录model信息
                inputs["model"] = args[0].model
                # 从第二个参数开始记录
                func_args = args[1:]
            else:
                func_args = args
            
            # 获取函数参数名
            import inspect
            sig = inspect.signature(func)
            param_names = list(sig.parameters.keys())
            
            # 填充位置参数
            for i, arg in enumerate(func_args):
                if i < len(param_names):
                    # 如果是图像数据，不记录具体内容，只记录图像路径或对象类型
                    if param_names[i] == 'image_path' and isinstance(arg, str):
                        inputs[param_names[i]] = f"Image: {os.path.basename(arg)}"
                    elif param_names[i] == 'pil_image' and hasattr(arg, 'size'):
                        inputs[param_names[i]] = f"PIL Image: {arg.size}"
                    elif param_names[i] == 'images' and isinstance(arg, list):
                        inputs[param_names[i]] = f"Images: {len(arg)} items"
                    else:
                        inputs[param_names[i]] = arg
            
            # 填充关键字参数
            for k, v in kwargs.items():
                # 同样处理图像数据
                if k == 'image_path' and isinstance(v, str):
                    inputs[k] = f"Image: {os.path.basename(v)}"
                elif k == 'pil_image' and hasattr(v, 'size'):
                    inputs[k] = f"PIL Image: {v.size}"
                elif k == 'images' and isinstance(v, list):
                    inputs[k] = f"Images: {len(v)} items"
                else:
                    inputs[k] = v
            
            try:
                # 调用原始函数
                result = func(*args, **kwargs)
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                # 提取最重要的内容，对于prompt提取提示词
                prompt = ""
                if "prompt" in inputs:
                    prompt = inputs["prompt"]
                elif "self" in inputs:
                    prompt = inputs["self"]
                
                # 记录成功的调用
                log_llm_call(
                    function_name=function_name,
                    inputs={"prompt": prompt, **inputs},
                    outputs=result,
                    duration_ms=duration_ms
                )
                return result
            except Exception as e:
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                # 提取最重要的内容，对于prompt提取提示词
                prompt = ""
                if "prompt" in inputs:
                    prompt = inputs["prompt"]
                elif "self" in inputs:
                    prompt = inputs["self"]
                
                # 记录失败的调用
                log_llm_call(
                    function_name=function_name,
                    inputs={"prompt": prompt, **inputs},
                    outputs=None,
                    error=str(e),
                    duration_ms=duration_ms
                )
                raise  # 重新抛出异常，不影响原有流程
                
        return wrapper
