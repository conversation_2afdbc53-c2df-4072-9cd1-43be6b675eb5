"""
异步任务处理模块
用于在后台处理耗时操作，如AI分析任务
"""

import threading
import queue
import time
import json
import csv
import io
from typing import Dict, Any, Optional, Callable, List, Tuple
import os

# 导入分析相关模块
from modules.ai_analyzer import analyze_chart_data

# 任务队列
task_queue = queue.Queue()

# 任务状态和结果字典
task_status = {}
task_results = {}

# 任务类型
TASK_TYPE_ANALYZE_CHART = "analyze_chart"

# 分析任务状态
STATUS_PENDING = "pending"
STATUS_PROCESSING = "processing"
STATUS_COMPLETED = "completed"
STATUS_FAILED = "failed"

# 是否已启动工作线程
worker_started = False

def init_task_worker():
    """初始化任务处理线程"""
    global worker_started
    if not worker_started:
        worker_thread = threading.Thread(target=task_worker, daemon=True)
        worker_thread.start()
        worker_started = True
        print("任务处理线程已启动")

def task_worker():
    """任务处理工作线程，在后台运行"""
    print("任务处理线程开始运行")
    while True:
        try:
            # 从队列中获取任务
            task = task_queue.get(timeout=1)
            if not task:
                continue
                
            task_id = task.get('id')
            task_type = task.get('type')
            params = task.get('params', {})
            
            # 更新任务状态为处理中
            task_status[task_id] = STATUS_PROCESSING
            
            # 根据任务类型执行不同的处理
            if task_type == TASK_TYPE_ANALYZE_CHART:
                _process_analyze_chart_task(task_id, params)
            
            # 标记任务完成
            task_queue.task_done()
            
        except queue.Empty:
            # 队列为空，继续等待
            pass
        except Exception as e:
            print(f"任务处理发生错误: {str(e)}")
            # 如果有当前任务ID，标记为失败
            if 'task_id' in locals() and task_id:
                task_status[task_id] = STATUS_FAILED
                task_results[task_id] = {"error": str(e)}
            
            time.sleep(0.5)  # 错误后短暂暂停




def _process_analyze_chart_task(task_id: str, params: Dict[str, Any]):
    """处理图表分析任务"""
    try:
        chart_type = params.get('chart_type')
        force_new = params.get('force_new', False)
        
        print(f"处理图表分析任务: {task_id}, 类型: {chart_type}")

        # 从CSV文件中提取数据
        print(f"尝试读取CSV文件以提取 {chart_type} 类型的数据")
        chart_data = _extract_chart_data_from_csv(chart_type)
        
        if not chart_data:
            raise ValueError(f"无法提取 {chart_type} 类型的数据")

        # 使用AI分析器模块进行分析
        print(f"开始分析图表数据: {chart_type}")
        print(f"图表数据: {chart_data}")
        result = analyze_chart_data(chart_type, chart_data, force_new)
        
        # 处理分析结果
        _handle_analysis_result(task_id, result, chart_type)
        
    except Exception as e:
        print(f"图表分析任务处理失败: {str(e)}")
        task_status[task_id] = STATUS_FAILED
        task_results[task_id] = {"error": str(e)}


def _extract_chart_data_from_csv(chart_type: str) -> str:
    """从CSV文件中提取图表数据"""
    import csv
    import os
    import io
    
    # 图表类型到CSV部分名称的映射
    CHART_TYPE_MAPPING = {
        "focal_lengths": "焦距分布",
        "apertures": "光圈分布", 
        "shutter_speeds": "快门速度分布",
        "iso_values": "ISO分布",
        "devices": "设备分布",
        "lenses": "镜头分布",
        "month": "月份分布",
        "time": "时间段分布"
    }
    
    # 获取目标部分名称
    section_name = CHART_TYPE_MAPPING.get(chart_type, "总体统计")
    
    # 读取基础分析CSV文件 - 使用绝对路径
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    csv_path = os.path.join(current_dir, 'data', 'basic_analysis.csv')
    
    print(f"读取CSV文件: {csv_path}")
    print(f"查找数据部分: {section_name}")
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            rows = list(csv_reader)
            
            if not rows:
                raise ValueError("CSV文件为空")
            
            # 获取表头
            header = rows[0]
            
            # 提取目标部分数据
            target_section = _extract_section_data(rows, section_name)
            
            if not target_section:
                print(f"警告: 未找到 {section_name} 部分的数据")
                return ""
            
            # 将提取的数据转换为CSV格式的字符串
            chart_data = _convert_to_csv_string(header, target_section)
            
            print(f"成功提取 {len(target_section)} 行数据")
            return chart_data
            
    except FileNotFoundError:
        print(f"错误: CSV文件不存在: {csv_path}")
        return ""
    except Exception as e:
        print(f"读取CSV文件时出错: {str(e)}")
        return ""


def _extract_section_data(rows: List[List[str]], section_name: str) -> List[List[str]]:
    """从CSV行中提取特定部分的数据"""
    target_section = []
    in_target_section = False
    
    for row in rows:
        if len(row) == 0:
            continue
            
        # 检查是否找到目标部分的开始
        if row[0].strip() == section_name:
            in_target_section = True
            target_section.append(row)
            print(f"找到目标部分: {row}")
            continue
        
        # 如果在目标部分中
        if in_target_section:
            # 检查是否是数据行（第一列为空，第二列有内容）
            if len(row) > 1 and row[0].strip() == "" and row[1].strip() != "":
                target_section.append(row)
                print(f"添加数据行: {row}")
            # 检查是否遇到了下一个部分（非空第一列且不是当前部分名称）
            elif row[0].strip() != "" and row[0].strip() != section_name:
                print(f"找到下一个部分，停止提取: {row}")
                break
            # 跳过空行
            elif len(row) <= 1 or all(field.strip() == "" for field in row):
                continue
    
    return target_section


def _convert_to_csv_string(header: List[str], data_rows: List[List[str]]) -> str:
    """将表头和数据行转换为CSV字符串"""
    import csv
    import io
    
    output = io.StringIO()
    csv_writer = csv.writer(output)
    
    # 写入表头
    csv_writer.writerow(header)
    
    # 写入数据行
    for row in data_rows:
        csv_writer.writerow(row)
    
    return output.getvalue()


def _handle_analysis_result(task_id: str, result: Dict[str, Any], chart_type: str):
    """处理分析结果"""
    if result.get("success"):
        # 分析成功
        task_status[task_id] = STATUS_COMPLETED
        
        # 返回结果包括文本分析和结构化数据
        task_results[task_id] = {
            "analysis": result.get("analysis", ""),
            "structured": result.get("structured", {}),
            "chart_type": chart_type,
            "cached": result.get("cached", False)
        }
        
        print(f"任务 {task_id} 分析成功完成")
    else:
        # 分析失败
        task_status[task_id] = STATUS_FAILED
        task_results[task_id] = {"error": result.get("message", "LLM分析生成失败")}
        
        print(f"任务 {task_id} 分析失败: {result.get('message', '未知错误')}")

def submit_chart_analysis_task(chart_type: str, chart_data: str, force_new: bool = False) -> str:
    """
    提交图表分析任务
    
    Args:
        chart_type: 图表类型
        chart_data: 图表数据
        force_new: 是否强制重新分析
    
    Returns:
        str: 任务ID
    """
    # 确保工作线程已启动
    init_task_worker()
    
    # 生成任务ID
    task_id = f"chart_analysis_{chart_type}_{int(time.time())}"
    
    # 创建任务
    task = {
        "id": task_id,
        "type": TASK_TYPE_ANALYZE_CHART,
        "params": {
            "chart_type": chart_type,
            "chart_data": chart_data,
            "force_new": force_new
        },
        "created_at": time.time()
    }
    
    # 更新任务状态
    task_status[task_id] = STATUS_PENDING
    
    # 提交任务到队列
    task_queue.put(task)
    
    return task_id

def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
    
    Returns:
        Dict: 包含任务状态的字典
    """
    status = task_status.get(task_id, None)
    result = None
    
    if status == STATUS_COMPLETED:
        result = task_results.get(task_id, {})
    elif status == STATUS_FAILED:
        result = task_results.get(task_id, {"error": "未知错误"})
    
    return {
        "id": task_id,
        "status": status,
        "result": result
    }

def clean_old_tasks(max_age: int = 3600):
    """
    清理旧任务
    
    Args:
        max_age: 最大任务保留时间（秒）
    """
    current_time = time.time()
    # 查找需要清理的任务
    to_clean = []
    
    for task_id, status in task_status.items():
        if status in [STATUS_COMPLETED, STATUS_FAILED]:
            # 从任务ID中提取时间戳
            try:
                # 格式为chart_analysis_类型_时间戳
                parts = task_id.split('_')
                if len(parts) > 2:
                    timestamp = int(parts[-1])
                    if current_time - timestamp > max_age:
                        to_clean.append(task_id)
            except:
                pass
    
    # 清理任务
    for task_id in to_clean:
        if task_id in task_status:
            del task_status[task_id]
        if task_id in task_results:
            del task_results[task_id]

# 定期清理旧任务的函数
def start_cleaning_thread():
    """启动定期清理任务的线程"""
    def cleaning_worker():
        while True:
            try:
                time.sleep(1800)  # 每30分钟清理一次
                clean_old_tasks()
            except Exception as e:
                print(f"清理任务时出错: {str(e)}")
    
    cleaning_thread = threading.Thread(target=cleaning_worker, daemon=True)
    cleaning_thread.start()

# 启动清理线程
start_cleaning_thread()
