#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容分析自动检查器
检查是否有未分析的照片，自动触发ContentAnalyzer进行分析
"""

import os
import sys
import csv
import json
import threading
import time
from typing import Dict, List, Optional, Tuple
from modules.content_analyzer import ContentAnalyzer
from modules.content_analyzer_config import get_analysis_config
from modules.utils import Colors


class ContentAutoChecker:
    """内容分析自动检查器"""
    
    def __init__(self):
        """初始化检查器"""
        self.analysis_config = get_analysis_config()
        self.config = self.analysis_config["config"]
        self.csv_input_path = self.config["csv_input_path"]
        self.csv_output_path = self.config["csv_output_path"]
        self.report_output_path = self.config["report_output_path"]
    
    def check_unanalyzed_photos(self) -> <PERSON><PERSON>[bool, Dict[str, int]]:
        """
        检查是否有未分析的照片
        
        Returns:
            Tuple[bool, Dict[str, int]]: (是否有未分析的照片, 分析状态统计)
        """
        # 如果没有原始数据文件，则没有需要分析的照片
        if not os.path.exists(self.csv_input_path):
            return False, {"total": 0, "analyzed": 0, "unanalyzed": 0}
        
        try:
            # 加载原始数据
            raw_data = []
            with open(self.csv_input_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 清理BOM字符
                    cleaned_row = {}
                    for key, value in row.items():
                        clean_key = key.replace('\ufeff', '') if key else key
                        cleaned_row[clean_key] = value
                    raw_data.append(cleaned_row)
            
            if not raw_data:
                return False, {"total": 0, "analyzed": 0, "unanalyzed": 0}
            
            # 如果已分析文件存在，加载它
            analyzed_data = {}
            if os.path.exists(self.csv_output_path):
                try:
                    with open(self.csv_output_path, 'r', encoding='utf-8-sig') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            # 清理BOM字符
                            cleaned_row = {}
                            for key, value in row.items():
                                clean_key = key.replace('\ufeff', '') if key else key
                                cleaned_row[clean_key] = value
                            
                            # 使用文件路径作为键
                            file_path = cleaned_row.get('路径', '')
                            if file_path:
                                analyzed_data[file_path] = cleaned_row
                except Exception:
                    pass
            
            # 统计分析状态
            total = len(raw_data)
            analyzed = 0
            unanalyzed = 0
            
            for row in raw_data:
                file_path = row.get('路径', '')
                
                # 检查是否已分析
                if file_path in analyzed_data:
                    analyzed_row = analyzed_data[file_path]
                    score = analyzed_row.get('评分', '')
                    tone = analyzed_row.get('色调', '')
                    tags = analyzed_row.get('题材标签', '')
                    
                    # 检查分析是否完整
                    if (score and str(score).strip() and str(score).strip() != '0' and 
                        tone and str(tone).strip() and str(tone).strip() != '未知' and
                        tags and str(tags).strip() and str(tags).strip() != '其他'):
                        analyzed += 1
                    else:
                        unanalyzed += 1
                else:
                    unanalyzed += 1
            
            status = {
                "total": total,
                "analyzed": analyzed,
                "unanalyzed": unanalyzed
            }
            
            has_unanalyzed = unanalyzed > 0
            return has_unanalyzed, status
            
        except Exception as e:
            print(f"{Colors.RED}检查未分析照片时出错：{str(e)}{Colors.END}")
            return False, {"total": 0, "analyzed": 0, "unanalyzed": 0}
    
    def auto_analyze_if_needed(self, max_analyze: int = 50) -> bool:
        """
        如果有未分析的照片，自动运行ContentAnalyzer
        
        Args:
            max_analyze: 最大分析数量（避免一次性分析太多）
            
        Returns:
            bool: 是否执行了分析
        """
        try:
            has_unanalyzed, status = self.check_unanalyzed_photos()
            
            if not has_unanalyzed:
                print(f"{Colors.GREEN}所有照片都已分析完成{Colors.END}")
                return False
            
            print(f"{Colors.BLUE}发现 {status['unanalyzed']} 张未分析照片，开始自动分析...{Colors.END}")
            
            # 创建ContentAnalyzer实例
            analyzer = ContentAnalyzer()
            
            # 测试LLM连接
            if not analyzer.test_llm_connection():
                print(f"{Colors.RED}LLM服务不可用，跳过自动分析{Colors.END}")
                return False
            
            # 如果要分析的照片数量很多，分批处理
            if max_analyze > 1000 or status['unanalyzed'] > 1000:
                print(f"{Colors.BLUE}照片数量较多，将分批处理以避免内存问题{Colors.END}")
                
                batch_size = 100  # 每批处理100张
                total_processed = 0
                
                while total_processed < status['unanalyzed'] and (max_analyze > 1000 or total_processed < max_analyze):
                    current_batch = min(batch_size, status['unanalyzed'] - total_processed)
                    if max_analyze <= 1000:
                        current_batch = min(current_batch, max_analyze - total_processed)
                    
                    print(f"{Colors.BLUE}处理第 {total_processed + 1} 到 {total_processed + current_batch} 张照片{Colors.END}")
                    
                    # 运行批量分析
                    analyzer.run_batch_analysis(start_index=total_processed, max_images=current_batch)
                    
                    total_processed += current_batch
                    
                    # 检查是否还有未分析的照片
                    has_unanalyzed, updated_status = self.check_unanalyzed_photos()
                    if not has_unanalyzed:
                        print(f"{Colors.GREEN}所有照片都已分析完成！{Colors.END}")
                        break
                    
                    print(f"{Colors.BLUE}已处理 {total_processed} 张照片，还有 {updated_status['unanalyzed']} 张待分析{Colors.END}")
                
                print(f"{Colors.GREEN}批量分析完成，共处理 {total_processed} 张照片{Colors.END}")
            else:
                # 数量较少，一次性处理
                analyze_count = min(max_analyze, status['unanalyzed'])
                print(f"{Colors.BLUE}将分析 {analyze_count} 张照片{Colors.END}")
                
                # 运行批量分析
                analyzer.run_batch_analysis(start_index=0, max_images=analyze_count)
            
            # 生成分析报告
            analyzer.create_analysis_report()
            
            print(f"{Colors.GREEN}自动分析完成{Colors.END}")
            return True
            
        except Exception as e:
            print(f"{Colors.RED}自动分析过程中出错：{str(e)}{Colors.END}")
            return False
    
    def run_background_check(self, max_analyze: int = 20):
        """
        在后台线程中运行检查和分析
        
        Args:
            max_analyze: 最大分析数量
        """
        def background_task():
            try:
                print(f"{Colors.BLUE}后台检查未分析照片...{Colors.END}")
                self.auto_analyze_if_needed(max_analyze)
            except Exception as e:
                print(f"{Colors.RED}后台分析任务失败：{str(e)}{Colors.END}")
        
        # 启动后台线程
        thread = threading.Thread(target=background_task, daemon=True)
        thread.start()
    
    def get_analysis_report(self) -> Optional[Dict]:
        """
        获取分析报告数据
        
        Returns:
            Optional[Dict]: 分析报告数据，如果不存在则返回None
        """
        if not os.path.exists(self.report_output_path):
            return None
        
        try:
            with open(self.report_output_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"{Colors.RED}读取分析报告失败：{str(e)}{Colors.END}")
            return None
    
    def check_and_update_report(self) -> bool:
        """
        检查并更新分析报告
        
        Returns:
            bool: 是否更新了报告
        """
        try:
            # 检查是否有已分析的数据
            if not os.path.exists(self.csv_output_path):
                return False
            
            # 检查报告是否存在或需要更新
            if not os.path.exists(self.report_output_path):
                # 报告不存在，生成新报告
                analyzer = ContentAnalyzer()
                analyzer.create_analysis_report()
                return True
            
            # 检查报告是否过期（CSV文件更新时间晚于报告）
            csv_mtime = os.path.getmtime(self.csv_output_path)
            report_mtime = os.path.getmtime(self.report_output_path)
            
            if csv_mtime > report_mtime:
                # CSV文件更新了，重新生成报告
                print(f"{Colors.BLUE}检测到数据更新，重新生成分析报告...{Colors.END}")
                analyzer = ContentAnalyzer()
                analyzer.create_analysis_report()
                return True
            
            return False
            
        except Exception as e:
            print(f"{Colors.RED}检查报告更新时出错：{str(e)}{Colors.END}")
            return False


def check_and_auto_analyze(max_analyze: Optional[int] = None) -> Tuple[bool, Optional[Dict]]:
    """
    便捷函数：检查并自动分析未分析的照片，返回分析报告
    
    Args:
        max_analyze: 最大分析数量，None表示不限制
        
    Returns:
        Tuple[bool, Optional[Dict]]: (是否执行了分析, 分析报告数据)
    """
    checker = ContentAutoChecker()
    
    # 检查并分析，如果max_analyze为None，则使用默认的较大值
    if max_analyze is None:
        max_analyze = 9999  # 设置一个足够大的值，相当于不限制
    
    analyzed = checker.auto_analyze_if_needed(max_analyze)
    
    # 更新报告
    checker.check_and_update_report()
    
    # 获取报告
    report = checker.get_analysis_report()
    
    return analyzed, report


def get_current_analysis_status() -> Dict[str, int]:
    """
    便捷函数：获取当前分析状态
    
    Returns:
        Dict[str, int]: 分析状态统计
    """
    checker = ContentAutoChecker()
    has_unanalyzed, status = checker.check_unanalyzed_photos()
    return status


if __name__ == "__main__":
    # 测试用法
    checker = ContentAutoChecker()
    has_unanalyzed, status = checker.check_unanalyzed_photos()
    
    print(f"分析状态：")
    print(f"  总照片数：{status['total']}")
    print(f"  已分析：{status['analyzed']}")
    print(f"  待分析：{status['unanalyzed']}")
    
    if has_unanalyzed:
        print("\n开始自动分析...")
        checker.auto_analyze_if_needed()