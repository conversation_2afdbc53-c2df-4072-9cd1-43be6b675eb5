#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新增照片检测器
用于检测配置路径中的新增照片并进行数据提取
"""

import os
import csv
import json
import time
from datetime import datetime
from typing import List, Dict, Set, Tuple
from modules.batch_photo_analyzer import BatchPhotoAnalyzer
from modules.utils import Colors


class NewPhotoDetector:
    """新增照片检测器"""
    
    def __init__(self):
        self.batch_analyzer = BatchPhotoAnalyzer()
        self.supported_formats = ['.jpg', '.jpeg', '.tiff', '.tif', '.png']
        self.config_file = "./config/config.json"  # 更新配置文件路径
        self.raw_data_file = "./data/raw.csv"
        self.processed_photos_file = "./data/processed_photos.json"
        
    def load_config(self) -> Dict:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"paths": []}
    
    def load_processed_photos(self) -> Dict[str, float]:
        """加载已处理照片记录"""
        if os.path.exists(self.processed_photos_file):
            with open(self.processed_photos_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_processed_photos(self, processed_photos: Dict[str, float]):
        """保存已处理照片记录"""
        os.makedirs(os.path.dirname(self.processed_photos_file), exist_ok=True)
        with open(self.processed_photos_file, 'w', encoding='utf-8') as f:
            json.dump(processed_photos, f, ensure_ascii=False, indent=2)
    
    def scan_directory_for_photos(self, directory: str) -> List[str]:
        """扫描目录中的所有照片文件"""
        photo_files = []
        
        if not os.path.exists(directory):
            print(f"{Colors.YELLOW}目录不存在: {directory}{Colors.END}")
            return photo_files
        
        if not os.path.isdir(directory):
            print(f"{Colors.YELLOW}不是目录: {directory}{Colors.END}")
            return photo_files
        
        # 递归查找所有支持的图像文件
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_lower = file.lower()
                if any(file_lower.endswith(ext) for ext in self.supported_formats):
                    photo_files.append(os.path.join(root, file))
        
        return sorted(photo_files)
    
    def get_file_modification_time(self, file_path: str) -> float:
        """获取文件的修改时间"""
        try:
            return os.path.getmtime(file_path)
        except OSError:
            return 0.0
    
    def detect_new_photos(self) -> Tuple[List[str], int]:
        """检测新增照片"""
        print(f"\n{Colors.BLUE}开始检测新增照片...{Colors.END}")
        
        config = self.load_config()
        paths = config.get("paths", [])
        
        if not paths:
            print(f"{Colors.YELLOW}没有配置照片路径，跳过新增照片检测{Colors.END}")
            return [], 0
        
        # 加载已处理照片记录
        processed_photos = self.load_processed_photos()
        
        # 扫描所有配置路径
        all_current_photos = []
        for path in paths:
            print(f"{Colors.BLUE}扫描路径: {path}{Colors.END}")
            photos = self.scan_directory_for_photos(path)
            all_current_photos.extend(photos)
            print(f"{Colors.GREEN}找到 {len(photos)} 张照片{Colors.END}")
        
        # 检测新增照片
        new_photos = []
        for photo_path in all_current_photos:
            current_mtime = self.get_file_modification_time(photo_path)
            
            # 如果照片不在已处理记录中，或者文件已被修改，认为是新增/更新的照片
            if (photo_path not in processed_photos or 
                current_mtime > processed_photos.get(photo_path, 0)):
                new_photos.append(photo_path)
        
        # 清理已删除的照片记录
        existing_photos = set(all_current_photos)
        processed_photos = {path: mtime for path, mtime in processed_photos.items() 
                          if path in existing_photos}
        
        print(f"{Colors.GREEN}检测完成，共发现 {len(new_photos)} 张新增/更新照片{Colors.END}")
        return new_photos, len(all_current_photos)
    
    def process_new_photos(self, new_photos: List[str]) -> bool:
        """处理新增照片"""
        if not new_photos:
            return True
        
        print(f"\n{Colors.BLUE}开始处理新增照片...{Colors.END}")
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.raw_data_file), exist_ok=True)
        
        # 处理每张新照片
        processed_count = 0
        for i, photo_path in enumerate(new_photos, 1):
            try:
                print(f"处理 ({i}/{len(new_photos)}): {os.path.basename(photo_path)}")
                
                # 使用批量分析器处理单个文件
                success = self.batch_analyzer.process_single_file(photo_path, self.raw_data_file)
                if success:
                    processed_count += 1
                    
            except Exception as e:
                print(f"{Colors.RED}处理照片 {photo_path} 失败: {str(e)}{Colors.END}")
                continue
        
        # 更新已处理照片记录
        processed_photos = self.load_processed_photos()
        for photo_path in new_photos:
            processed_photos[photo_path] = self.get_file_modification_time(photo_path)
        self.save_processed_photos(processed_photos)
        
        print(f"{Colors.GREEN}新增照片处理完成，成功处理 {processed_count} 张照片{Colors.END}")
        return processed_count > 0
    
    def check_and_process_new_photos(self) -> Tuple[bool, str]:
        """检查并处理新增照片"""
        try:
            start_time = time.time()
            
            # 检测新增照片
            new_photos, total_photos = self.detect_new_photos()
            
            if not new_photos:
                message = f"所有照片均已处理（共 {total_photos} 张）"
                print(f"{Colors.GREEN}{message}{Colors.END}")
                return True, message
            
            # 处理新增照片
            success = self.process_new_photos(new_photos)
            
            elapsed_time = time.time() - start_time
            
            if success:
                message = f"成功处理 {len(new_photos)} 张新增照片，耗时 {elapsed_time:.2f} 秒"
                print(f"{Colors.GREEN}{message}{Colors.END}")
                return True, message
            else:
                message = f"处理新增照片失败"
                print(f"{Colors.RED}{message}{Colors.END}")
                return False, message
                
        except Exception as e:
            message = f"检测新增照片过程中发生错误: {str(e)}"
            print(f"{Colors.RED}{message}{Colors.END}")
            return False, message


def main():
    """主函数 - 用于测试"""
    detector = NewPhotoDetector()
    success, message = detector.check_and_process_new_photos()
    print(f"\n结果: {message}")


if __name__ == "__main__":
    main()
