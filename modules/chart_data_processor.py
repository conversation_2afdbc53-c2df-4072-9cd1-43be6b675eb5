#!/usr/bin/env python3
"""
独立的数据处理和图表生成模块
重新实现所有数据读取和图表显示逻辑
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import os
import json
import time
import numpy as np
from typing import Dict, Any, Optional
from plotly.utils import PlotlyJSONEncoder


class PhotoDataProcessor:
    """照片数据处理器"""
    
    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path
        self.raw_data = {}
        self.processed_data = {}
    
    def load_and_parse_csv(self) -> bool:
        """加载并解析CSV文件"""
        if not os.path.exists(self.csv_file_path):
            print(f"❌ CSV文件不存在: {self.csv_file_path}")
            return False
        
        try:
            # 读取CSV文件
            df = pd.read_csv(self.csv_file_path, encoding='utf-8-sig')
            print(f"✅ 成功读取CSV文件，共{len(df)}行")
            
            # 初始化数据结构
            self.raw_data = {
                'total_photos': 0,
                'focal_lengths': {},
                'apertures': {},
                'shutter_speeds': {},
                'iso_values': {},
                'devices': {},
                'lenses': {},
                'months': {},
                'time_periods': {}
            }
            
            # 解析数据
            current_category = None
            data_rows_count = 0
            
            for index, row in df.iterrows():
                # 获取行数据
                category = str(row['分析类型']).strip() if pd.notna(row['分析类型']) else ''
                subcategory = str(row['区间/类别']).strip() if pd.notna(row['区间/类别']) else ''
                
                # 安全转换数量
                try:
                    count = int(float(row['照片数量'])) if pd.notna(row['照片数量']) else 0
                except (ValueError, TypeError):
                    count = 0
                
                # 调试输出
                print(f"行{index+1:2d}: '{category}' | '{subcategory}' | {count}")
                
                # 处理总体统计
                if category == '总体统计' and subcategory == '总照片数量':
                    self.raw_data['total_photos'] = count
                    print(f"    ✅ 总照片数量: {count}")
                    continue
                
                # 识别分类标题（以'分布'结尾）
                if category and category.endswith('分布'):
                    current_category = category
                    print(f"    📂 当前分类: {current_category}")
                    continue
                
                # 处理数据行（分类为空，但有子分类）
                if not category and subcategory and current_category:
                    data_rows_count += 1
                    print(f"    📊 数据: {current_category} -> {subcategory} = {count}")
                    
                    # 分配到对应的数据字典
                    if current_category == '焦距分布':
                        self.raw_data['focal_lengths'][subcategory] = count
                    elif current_category == '光圈分布':
                        self.raw_data['apertures'][subcategory] = count
                    elif current_category == '快门速度分布':
                        self.raw_data['shutter_speeds'][subcategory] = count
                    elif current_category == 'ISO分布':
                        self.raw_data['iso_values'][subcategory] = count
                    elif current_category == '设备分布':
                        self.raw_data['devices'][subcategory] = count
                    elif current_category == '镜头分布':
                        self.raw_data['lenses'][subcategory] = count
                    elif current_category == '月份分布':
                        self.raw_data['months'][subcategory] = count
                    elif current_category == '时间段分布':
                        self.raw_data['time_periods'][subcategory] = count
            
            print(f"\n✅ 解析完成，处理了{data_rows_count}行数据")
            
            # 输出统计信息
            self._print_data_summary()
            
            return True
            
        except Exception as e:
            print(f"❌ CSV解析失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _print_data_summary(self):
        """打印数据摘要"""
        print(f"\n📊 数据摘要:")
        print(f"总照片数量: {self.raw_data['total_photos']}")
        
        categories = [
            ('焦距分布', 'focal_lengths'),
            ('光圈分布', 'apertures'),
            ('快门速度分布', 'shutter_speeds'),
            ('ISO分布', 'iso_values'),
            ('设备分布', 'devices'),
            ('镜头分布', 'lenses'),
            ('月份分布', 'months'),
            ('时间段分布', 'time_periods')
        ]
        
        for name, key in categories:
            data = self.raw_data[key]
            if data:
                total_items = len(data)
                non_zero_items = sum(1 for v in data.values() if v > 0)
                print(f"  {name}: {total_items} 项 ({non_zero_items} 项有数据)")
                
                # 显示具体数据
                for item, count in data.items():
                    status = "✅" if count > 0 else "⚪"
                    print(f"    {status} {item}: {count}")
            else:
                print(f"  {name}: 无数据")
    
    def process_data_for_charts(self):
        """处理数据用于图表生成"""
        self.processed_data = {}
        
        for category, data in self.raw_data.items():
            if isinstance(data, dict):
                # 过滤掉无效和零值数据
                filtered_data = {}
                for key, value in data.items():
                    if (key and str(key).strip() and 
                        str(key) not in ['未知', 'nan', 'None'] and 
                        value > 0):
                        filtered_data[key] = value
                
                self.processed_data[category] = filtered_data
            else:
                self.processed_data[category] = data
        
        print(f"\n🎯 过滤后的图表数据:")
        for category, data in self.processed_data.items():
            if isinstance(data, dict) and data:
                print(f"  {category}: {len(data)} 项")
                for item, count in data.items():
                    print(f"    ✅ {item}: {count}")
                
                # 验证数据有效性
                if sum(data.values()) == 0:
                    print(f"  ⚠️ 警告: {category} 所有值均为0")
        
        # 检查是否有空字典
        empty_categories = [cat for cat, data in self.processed_data.items() 
                          if isinstance(data, dict) and not data]
        if empty_categories:
            print(f"  ⚠️ 警告: 以下分类没有有效数据: {', '.join(empty_categories)}")
        
        # 数据处理完成后，自动检查并触发AI分析
        self._auto_trigger_analysis()
    
    def get_data_for_category(self, category: str) -> Dict[str, int]:
        """获取指定分类的数据"""
        return self.processed_data.get(category, {})
    
    def get_top_device_and_lens_info(self) -> Dict[str, Any]:
        """获取最多使用的设备、镜头及搭配信息"""
        devices = self.processed_data.get('devices', {})
        lenses = self.processed_data.get('lenses', {})
        months = self.processed_data.get('months', {})
        
        # 获取设备总数
        total_devices = len(devices)
        
        # 获取最多使用的设备
        top_device = ""
        top_device_count = 0
        if devices:
            top_device_item = max(devices.items(), key=lambda x: x[1])
            top_device = top_device_item[0]
            top_device_count = top_device_item[1]
        
        # 获取镜头总数
        total_lenses = len(lenses)
        
        # 获取最多使用的镜头
        top_lens = ""
        top_lens_count = 0
        if lenses:
            top_lens_item = max(lenses.items(), key=lambda x: x[1])
            top_lens = top_lens_item[0]
            top_lens_count = top_lens_item[1]
        
        # 计算实际的设备镜头搭配照片数量
        combo_count = 0
        device_lens_combo = "暂无数据"
        
        if top_device and top_lens:
            # 从原始数据中计算实际搭配数量
            if hasattr(self, 'raw_data') and 'photos' in self.raw_data:
                combo_count = 0
                for photo in self.raw_data['photos']:
                    if (photo.get('拍摄设备') == top_device and 
                        photo.get('镜头') == top_lens):
                        combo_count += 1
                        
            # 如果没有原始数据，使用估算方法
            if combo_count == 0:
                combo_count = min(top_device_count, top_lens_count)
                
            device_lens_combo = f"{top_device} + {top_lens}"
        
        # 获取最活跃的月份
        active_months = "暂无数据"
        if months:
            # 获取拍摄最多的前3个月
            sorted_months = sorted(months.items(), key=lambda x: x[1], reverse=True)[:3]
            month_names = []
            for month, count in sorted_months:
                if count > 0:
                    month_names.append(f"{month}月({count}张)")
            if month_names:
                active_months = ", ".join(month_names)
        
        return {
            'total_devices': total_devices,
            'top_device': top_device,
            'top_device_count': top_device_count,
            'total_lenses': total_lenses,
            'top_lens': top_lens,
            'top_lens_count': top_lens_count,
            'device_lens_combo': device_lens_combo,
            'device_lens_combo_count': combo_count,
            'active_months': active_months
        }
    
    def _auto_trigger_analysis(self):
        """自动检查并触发AI分析"""
        try:
            # 导入必要的模块
            from modules.analysis_storage import get_analysis_results, is_analysis_outdated
            from modules.ai_analyzer import analyze_chart_data
            from modules.llm_config import get_llm_config
            
            # 检查LLM是否启用
            llm_config = get_llm_config()
            if not llm_config.get("enabled", False):
                print("🔇 LLM服务未启用，跳过AI分析")
                return
            
            # 获取当前分析结果
            analysis_results = get_analysis_results()
            
            # 图表类型映射
            chart_type_mapping = {
                'focal_lengths': 'focal_lengths',
                'apertures': 'apertures', 
                'shutter_speeds': 'shutter_speeds',
                'iso_values': 'iso_values',
                'devices': 'devices',
                'lenses': 'lenses',
                'months': 'months',
                'time_periods': 'time_periods'
            }
            
            # 检查每个图表类型是否需要分析
            for data_key, chart_type in chart_type_mapping.items():
                if data_key in self.processed_data and self.processed_data[data_key]:
                    # 检查是否需要分析（基于CSV内容和照片数量变化）
                    needs_analysis = is_analysis_outdated(chart_type)
                    
                    if needs_analysis:
                        print(f"🤖 自动触发AI分析: {chart_type}")
                        
                        # 准备数据（不包含时间戳）
                        chart_data = json.dumps({
                            "type": chart_type,
                            "data": self.processed_data[data_key]
                        }, ensure_ascii=False)
                        
                        # 调用AI分析
                        try:
                            result = analyze_chart_data(chart_type, chart_data, force_new=True)
                            if result.get("success"):
                                print(f"✅ {chart_type} 分析完成")
                            else:
                                print(f"❌ {chart_type} 分析失败: {result.get('message', '未知错误')}")
                        except Exception as e:
                            print(f"❌ {chart_type} 分析异常: {str(e)}")
                    else:
                        print(f"⏭️ {chart_type} 分析数据有效，跳过")
        
        except Exception as e:
            print(f"❌ 自动触发AI分析时发生错误: {str(e)}")
            # 不阻止主流程，继续执行


class ChartGenerator:
    """图表生成器"""
    
    def __init__(self, data_processor: PhotoDataProcessor):
        self.data_processor = data_processor
        # 添加图表缓存字典
        self._chart_cache = {}
    
    def _extract_focal_length_number(self, focal_range: str) -> int:
        """从焦距范围中提取数字用于排序"""
        try:
            import re
            numbers = re.findall(r'\d+', focal_range)
            if numbers:
                return int(numbers[0])
            return 0
        except:
            return 0
    
    def create_bar_chart(self, category: str, title: str, 
                        x_label: str = "类别", y_label: str = "数量",
                        sort_by_value: bool = False) -> Optional[str]:
        """创建柱状图"""
        # 检查缓存
        cache_key = f"bar_{category}_{title}"
        if cache_key in self._chart_cache:
            print(f"✅ 使用缓存的{category}条形图")
            return self._chart_cache[cache_key]
            
        data = self.data_processor.get_data_for_category(category)
        
        if not data:
            print(f"❌ {category} 无数据，无法生成图表")
            return None
        
        print(f"🎨 生成{category}图表，数据: {data}")
        
        # 准备数据
        if sort_by_value and category == 'focal_lengths':
            # 焦距按数值排序
            sorted_items = sorted(data.items(), key=lambda x: self._extract_focal_length_number(x[0]))
            x_values = [item[0] for item in sorted_items]
            y_values = [item[1] for item in sorted_items]
        else:
            # 其他按原顺序或按值排序
            x_values = list(data.keys())
            y_values = list(data.values())
        
        print(f"    X轴: {x_values}")
        print(f"    Y轴: {y_values}")
        
        if not x_values or not y_values:
            print(f"❌ {category} 有效数据为空，无法生成图表")
            return None
            
        # 创建柱状图（使用graph_objects而不是express）
        fig = go.Figure(data=[
            go.Bar(
                x=x_values,
                y=y_values,
                text=y_values,
                textposition='auto',
                marker=dict(
                    color=y_values,
                    colorscale='Viridis',
                    line=dict(color='white', width=1),
                    opacity=0.8
                ),
                hovertemplate='<b>%{x}</b><br>%{y} 张照片<br><extra></extra>'
            )
        ])
        
        # 设置布局（简化配置，提高性能）
        fig.update_layout(
            title=dict(
                text=title,
                x=0.5,
                font=dict(size=18)
            ),
            xaxis=dict(
                title=x_label,
                tickangle=-45,
                showgrid=True
            ),
            yaxis=dict(
                title=y_label,
                showgrid=True
            ),
            showlegend=False,
            margin=dict(l=40, r=40, t=60, b=80),
            plot_bgcolor='white',
            paper_bgcolor='white',
            autosize=True,
            height=400,
            font=dict(family='Inter', color='#2d3748')
        )
        
        # 使用PlotlyJSONEncoder确保正确序列化数据
        result = json.dumps(fig, cls=PlotlyJSONEncoder)
        # 缓存结果
        self._chart_cache[cache_key] = result
        return result
    
    def create_pie_chart(self, category: str, title: str, 
                        legend_title: str = "分类", 
                        hole_size: float = 0.4) -> Optional[str]:
        """创建饼图"""
        # 检查缓存
        cache_key = f"pie_{category}_{title}"
        if cache_key in self._chart_cache:
            print(f"✅ 使用缓存的{category}饼图")
            return self._chart_cache[cache_key]
            
        data = self.data_processor.get_data_for_category(category)
        
        if not data:
            print(f"❌ {category} 无数据，无法生成饼图")
            return None
        
        print(f"🥧 生成{category}饼图，数据: {data}")
        
        values = list(data.values())
        names = list(data.keys())
        
        if not values or not names:
            print(f"❌ {category} 有效数据为空，无法生成饼图")
            return None
            
        # 确保数据有效（排除值为0的项）
        filtered_data = [(name, value) for name, value in zip(names, values) if value > 0]
        if not filtered_data:
            print(f"❌ {category} 所有值均为0，无法生成饼图")
            return None
            
        filtered_names = [item[0] for item in filtered_data]
        filtered_values = [item[1] for item in filtered_data]
        
        # 创建饼图（使用graph_objects而不是express）
        fig = go.Figure(data=[
            go.Pie(
                labels=filtered_names,
                values=filtered_values,
                textinfo='label+percent',
                insidetextorientation='radial',
                hole=0.4,  # 创建环形图，更现代的外观
                marker=dict(
                    colors=['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'],
                    line=dict(color='white', width=2)
                ),
                textfont=dict(size=12, color='white', family='Inter'),
                hovertemplate='<b>%{label}</b><br>%{value} 张照片<br>%{percent}<br><extra></extra>'
            )
        ])
        
        # 更新布局
        fig.update_layout(
            title=dict(
                text=title,
                x=0.5,
                font=dict(size=18, color='#2d3748', family='Inter')
            ),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=-0.2,
                xanchor="center",
                x=0.5,
                font=dict(color='#4a5568', family='Inter')
            ),
            margin=dict(l=50, r=50, t=80, b=120),
            autosize=True,
            height=450,
            paper_bgcolor='rgba(255,255,255,0)',
            plot_bgcolor='rgba(255,255,255,0)',
            font=dict(family='Inter', color='#2d3748')
        )
        
        # 使用PlotlyJSONEncoder确保正确序列化数据
        result = json.dumps(fig, cls=PlotlyJSONEncoder)
        # 缓存结果
        self._chart_cache[cache_key] = result
        return result
    
    def create_line_chart(self, category: str, title: str, 
                         x_label: str = "时间", y_label: str = "数量") -> Optional[str]:
        """创建曲线图"""
        # 检查缓存
        cache_key = f"line_{category}_{title}"
        if cache_key in self._chart_cache:
            print(f"✅ 使用缓存的{category}曲线图")
            return self._chart_cache[cache_key]
        
        data = self.data_processor.get_data_for_category(category)
        
        if not data:
            print(f"❌ {category} 无数据，无法生成曲线图")
            return None
        
        print(f"📈 生成{category}曲线图，数据: {data}")
        
        # 如果是月份数据，按月份顺序排序
        if category == 'months':
            month_order = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]
            sorted_items = []
            for month in month_order:
                if month in data:
                    sorted_items.append((month, data[month]))
            x_values = [item[0] for item in sorted_items]
            y_values = [item[1] for item in sorted_items]
        else:
            x_values = list(data.keys())
            y_values = list(data.values())
        
        if not x_values or not y_values:
            print(f"❌ {category} 有效数据为空，无法生成曲线图")
            return None
            
        # 创建平滑曲线图
        fig = go.Figure(data=[
            go.Scatter(
                x=x_values,
                y=y_values,
                mode='lines+markers',
                line=dict(
                    width=4, 
                    color='#667eea',
                    shape='spline',  # 添加平滑曲线
                    smoothing=1.3    # 设置平滑度
                ),
                marker=dict(
                    size=10, 
                    color='#764ba2',
                    line=dict(color='white', width=2),
                    symbol='circle'
                ),
                fill='tonexty',
                fillcolor='rgba(102, 126, 234, 0.1)',
                text=y_values,
                textposition='top center',
                hovertemplate='<b>%{x}</b><br>%{y} 张照片<br><extra></extra>',
                hoverinfo='text'
            )
        ])
        
        # 设置布局 - 简化配置提高性能
        fig.update_layout(
            title=dict(
                text=title,
                x=0.5,
                font=dict(size=18)
            ),
            xaxis=dict(
                title=x_label,
                showgrid=True
            ),
            yaxis=dict(
                title=y_label,
                showgrid=True
            ),
            showlegend=False,
            margin=dict(l=50, r=50, t=70, b=70),
            plot_bgcolor='white',
            autosize=True,
            height=400
        )
        
        # 使用PlotlyJSONEncoder确保正确序列化数据
        result = json.dumps(fig, cls=PlotlyJSONEncoder)
        # 缓存结果
        self._chart_cache[cache_key] = result
        return result
    
    def generate_all_data_charts(self) -> Dict[str, str]:
        """生成所有数据图表"""
        charts = {}
        
        # 焦距图表
        focal_chart = self.create_bar_chart(
            'focal_lengths', 
            '焦距使用统计', 
            '焦距范围', 
            '使用次数',
            sort_by_value=True
        )
        if focal_chart:
            charts['focal_lengths'] = focal_chart
        
        # 光圈图表
        aperture_chart = self.create_bar_chart(
            'apertures', 
            '光圈使用统计', 
            '光圈范围', 
            '使用次数'
        )
        if aperture_chart:
            charts['apertures'] = aperture_chart
        
        # 快门速度图表
        shutter_chart = self.create_bar_chart(
            'shutter_speeds', 
            '快门速度使用统计', 
            '快门速度范围', 
            '使用次数'
        )
        if shutter_chart:
            charts['shutter_speeds'] = shutter_chart
        
        # ISO图表
        iso_chart = self.create_bar_chart(
            'iso_values', 
            'ISO使用统计', 
            'ISO范围', 
            '使用次数'
        )
        if iso_chart:
            charts['iso_values'] = iso_chart
        
        return charts
    
    def generate_all_overview_charts(self) -> Dict[str, str]:
        """生成所有概览图表"""
        charts = {}
        
        # 设备饼图
        device_chart = self.create_pie_chart('devices', '拍摄设备分布')
        if device_chart:
            charts['devices'] = device_chart
        
        # 镜头饼图
        lens_chart = self.create_pie_chart('lenses', '镜头使用分布')
        if lens_chart:
            charts['lenses'] = lens_chart
        
        # 月份饼图
        month_chart = self.create_pie_chart('months', '拍摄月份分布')
        if month_chart:
            charts['months'] = month_chart
        
        # 时段饼图
        time_chart = self.create_pie_chart('time_periods', '拍摄时段分布')
        if time_chart:
            charts['time_periods'] = time_chart
        
        return charts


def test_data_processing():
    """测试数据处理"""
    print("="*80)
    print("🧪 测试独立的数据处理模块")
    print("="*80)
    
    # 创建数据处理器
    processor = PhotoDataProcessor("./data/basic_analysis.csv")
    
    # 加载和解析数据
    if not processor.load_and_parse_csv():
        print("❌ 数据加载失败")
        return None, None
    
    # 处理数据用于图表
    processor.process_data_for_charts()
    
    # 创建图表生成器
    chart_gen = ChartGenerator(processor)
    
    # 生成图表
    print("\n🎨 生成数据图表...")
    data_charts = chart_gen.generate_all_data_charts()
    
    print("\n🎨 生成概览图表...")
    overview_charts = chart_gen.generate_all_overview_charts()
    
    print(f"\n✅ 生成完成:")
    print(f"  数据图表: {list(data_charts.keys())}")
    print(f"  概览图表: {list(overview_charts.keys())}")
    
    return data_charts, overview_charts


if __name__ == "__main__":
    test_data_processing()
