#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片内容分析引擎
批量分析图片并为raw.csv添加评分、色调和题材标签
"""

import os
import sys
import csv
import json
import time
import argparse
from typing import Dict, List, Optional
from PIL import Image
from modules.llm_api import OllamaLLMAPI
from modules.llm_config import get_llm_config
from modules.utils import Colors
from modules.content_analyzer_config import get_analysis_config


class ContentAnalyzer:
    """图片内容分析器"""
    
    def __init__(self):
        """初始化分析器"""
        # 获取配置
        self.analysis_config = get_analysis_config()
        self.prompt = self.analysis_config["prompt"]
        self.schema = self.analysis_config["schema"]
        self.config = self.analysis_config["config"]
        
        # 初始化LLM API
        llm_config = get_llm_config()
        self.llm_api = OllamaLLMAPI(
            base_url=llm_config.get("base_url", "http://localhost:11434"),
            model=llm_config.get("model", "gemma3:4b")
        )
        
        # 设置路径
        self.csv_input_path = self.config["csv_input_path"]
        self.csv_output_path = self.config["csv_output_path"]
        self.batch_size = self.config["batch_size"]
        self.delay = self.config["delay_between_requests"]
    
    def load_csv_data(self) -> List[Dict]:
        """加载CSV数据"""
        # 首先加载原始数据
        if not os.path.exists(self.csv_input_path):
            print(f"{Colors.RED}错误：找不到原始数据文件 {self.csv_input_path}{Colors.END}")
            return []
        
        # 加载原始数据
        raw_data = []
        try:
            with open(self.csv_input_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 清理BOM字符
                    cleaned_row = {}
                    for key, value in row.items():
                        clean_key = key.replace('\ufeff', '') if key else key
                        cleaned_row[clean_key] = value
                    raw_data.append(cleaned_row)
            
            print(f"{Colors.GREEN}成功加载原始数据 {len(raw_data)} 条记录{Colors.END}")
        except Exception as e:
            print(f"{Colors.RED}加载原始数据失败：{str(e)}{Colors.END}")
            return []
        
        # 如果存在已分析文件，将分析结果合并到原始数据中
        if os.path.exists(self.csv_output_path):
            print(f"{Colors.BLUE}发现已分析文件，合并分析结果：{self.csv_output_path}{Colors.END}")
            try:
                analyzed_data = {}
                with open(self.csv_output_path, 'r', encoding='utf-8-sig') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        # 清理BOM字符
                        cleaned_row = {}
                        for key, value in row.items():
                            clean_key = key.replace('\ufeff', '') if key else key
                            cleaned_row[clean_key] = value
                        
                        # 使用文件路径作为键
                        file_path = cleaned_row.get('路径', '')
                        if file_path:
                            analyzed_data[file_path] = cleaned_row
                
                # 合并分析结果到原始数据
                for row in raw_data:
                    file_path = row.get('路径', '')
                    if file_path in analyzed_data:
                        analyzed_row = analyzed_data[file_path]
                        # 复制分析结果字段
                        row['评分'] = analyzed_row.get('评分', '')
                        row['色调'] = analyzed_row.get('色调', '')
                        row['题材标签'] = analyzed_row.get('题材标签', '')
                
                print(f"{Colors.GREEN}成功合并 {len(analyzed_data)} 条分析结果{Colors.END}")
            except Exception as e:
                print(f"{Colors.YELLOW}合并分析结果失败：{str(e)}{Colors.END}")
        
        return raw_data
    
    def analyze_single_image(self, image_path: str) -> Optional[Dict]:
        """分析单张图片"""
        if not os.path.exists(image_path):
            print(f"{Colors.YELLOW}警告：图片文件不存在 {image_path}{Colors.END}")
            return None
        
        try:
            # 检查图片格式 - 基于文件扩展名而不是PIL识别的格式
            supported_formats = self.config["supported_formats"]
            file_ext = os.path.splitext(image_path)[1].lower().lstrip('.')
            
            if file_ext not in [fmt.lower() for fmt in supported_formats]:
                print(f"{Colors.YELLOW}跳过不支持的图片格式：{image_path} (扩展名: {file_ext}){Colors.END}")
                return None
            
            # 验证图片是否可以打开
            try:
                with Image.open(image_path) as img:
                    # 确保图片可以正常加载
                    img.verify()
            except Exception as e:
                print(f"{Colors.YELLOW}跳过无法打开的图片：{image_path} ({str(e)}){Colors.END}")
                return None
            
            # 调用LLM分析
            print(f"{Colors.BLUE}正在深度分析：{os.path.basename(image_path)}{Colors.END}")
            print(f"{Colors.YELLOW}  使用专业摄影师视角进行四维度评估...{Colors.END}")
            
            result = self.llm_api.analyze_image_with_structured_output(
                image_path=image_path,
                prompt=self.prompt,
                schema=self.schema
            )
            
            # 检查是否有错误
            if "_error" in result:
                print(f"{Colors.RED}分析失败：{image_path}{Colors.END}")
                print(f"{Colors.RED}错误详情：{result['_error']}{Colors.END}")
                return None
            
            # 验证结果格式
            if not all(key in result for key in ['score', 'tone', 'tags']):
                print(f"{Colors.YELLOW}警告：分析结果格式不完整 {image_path}{Colors.END}")
                return None
            
            # 验证评分范围
            score = result.get('score', 0)
            if not (0 <= score <= 100):
                print(f"{Colors.YELLOW}警告：评分超出范围 {image_path}: {score}，已修正{Colors.END}")
                result['score'] = max(0, min(100, score))
            
            # 确保tags是列表
            if not isinstance(result['tags'], list):
                result['tags'] = [result['tags']]
            
            return result
            
        except Exception as e:
            print(f"{Colors.RED}分析图片时发生错误 {image_path}：{str(e)}{Colors.END}")
            return None
    
    def save_analyzed_data(self, data: List[Dict]) -> bool:
        """保存分析后的数据"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(self.csv_output_path), exist_ok=True)
            
            if not data:
                print(f"{Colors.RED}没有数据可保存{Colors.END}")
                return False
            
            # 添加新字段
            fieldnames = list(data[0].keys())
            new_fields = ['评分', '色调', '题材标签']
            
            for field in new_fields:
                if field not in fieldnames:
                    fieldnames.append(field)
            
            # 写入CSV文件
            with open(self.csv_output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            print(f"{Colors.GREEN}分析结果已保存到：{self.csv_output_path}{Colors.END}")
            return True
            
        except Exception as e:
            print(f"{Colors.RED}保存CSV文件错误：{str(e)}{Colors.END}")
            return False
    
    def save_single_analysis_result(self, row_data: Dict) -> bool:
        """保存单个分析结果到CSV文件（增量保存）"""
        try:
            file_exists = os.path.exists(self.csv_output_path)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(self.csv_output_path), exist_ok=True)
            
            # 定义字段名
            fieldnames = ['文件名', '路径', '拍摄设备', '镜头', '光圈', '快门', 'ISO', 
                         '等效35mm焦距', '位置', '拍摄日期', '时间', '图像尺寸', '文件大小', 
                         '经度', '纬度', '海拔', '评分', '色调', '题材标签']
            
            # 如果文件不存在，创建并写入header
            if not file_exists:
                with open(self.csv_output_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerow(row_data)
                return True
            
            # 文件存在，需要更新现有记录或追加新记录
            # 读取现有数据
            existing_data = []
            with open(self.csv_output_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    existing_data.append(row)
            
            # 查找是否存在相同路径的记录
            file_path = row_data.get('路径', '')
            found = False
            for i, existing_row in enumerate(existing_data):
                if existing_row.get('路径', '') == file_path:
                    # 更新现有记录
                    existing_data[i] = row_data
                    found = True
                    break
            
            # 如果没找到，添加新记录
            if not found:
                existing_data.append(row_data)
            
            # 重写整个文件
            with open(self.csv_output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(existing_data)
            
            return True
            
        except Exception as e:
            print(f"{Colors.RED}保存单个分析结果失败：{str(e)}{Colors.END}")
            return False
    
    def run_batch_analysis(self, start_index: int = 0, max_images: int = None) -> None:
        """运行批量分析"""
        print(f"{Colors.BLUE}{Colors.BOLD}开始批量图片内容分析{Colors.END}")
        print(f"{Colors.BLUE}{'=' * 50}{Colors.END}")
        print(f"{Colors.GREEN}使用增强AI分析模式：{Colors.END}")
        print(f"{Colors.GREEN}• 专业摄影师视角的深度分析{Colors.END}")
        print(f"{Colors.GREEN}• 四维度评分：构图、色彩、技术、创意{Colors.END}")
        print(f"{Colors.GREEN}• 详细的色调和题材分类{Colors.END}")
        print(f"{Colors.BLUE}{'=' * 50}{Colors.END}")
        
        # 加载CSV数据
        data = self.load_csv_data()
        if not data:
            return
        
        # 获取分析状态
        status = self.get_analysis_status(data)
        print(f"{Colors.BLUE}数据状态统计：{Colors.END}")
        print(f"{Colors.GREEN}• 总照片数：{status['total']}{Colors.END}")
        print(f"{Colors.GREEN}• 已分析：{status['analyzed']}{Colors.END}")
        print(f"{Colors.YELLOW}• 待分析：{status['unanalyzed']}{Colors.END}")
        
        if status['unanalyzed'] == 0:
            print(f"{Colors.GREEN}所有照片都已分析完成！{Colors.END}")
            return
        
        # 限制处理数量
        if max_images:
            data = data[start_index:start_index + max_images]
        else:
            data = data[start_index:]
        
        print(f"{Colors.BLUE}准备分析 {len(data)} 张图片（从第 {start_index + 1} 张开始）{Colors.END}")
        
        analyzed_count = 0
        skipped_count = 0
        error_count = 0
        
        # 批量处理
        for i, row in enumerate(data):
            image_path = row.get('路径', '')
            filename = row.get('文件名', '')
            
            print(f"\n{Colors.BLUE}[{i + 1}/{len(data)}] 处理：{filename}{Colors.END}")
            
            # 检查是否已经分析过 - 更严格的检查
            score = row.get('评分', '')
            tone = row.get('色调', '')
            tags = row.get('题材标签', '')
            
            # 如果评分不为空且不为0，且色调和标签都有值，则认为已分析
            if (score and str(score).strip() and str(score).strip() != '0' and 
                tone and str(tone).strip() and str(tone).strip() != '未知' and
                tags and str(tags).strip() and str(tags).strip() != '其他'):
                print(f"{Colors.YELLOW}已分析过 - 评分：{score}, 色调：{tone}, 标签：{tags}{Colors.END}")
                skipped_count += 1
                continue
            
            # 分析图片
            analysis_result = self.analyze_single_image(image_path)
            
            if analysis_result:
                # 更新数据
                row['评分'] = analysis_result['score']
                row['色调'] = analysis_result['tone']
                row['题材标签'] = ', '.join(analysis_result['tags'])
                analyzed_count += 1
                print(f"{Colors.GREEN}分析完成 - 评分：{analysis_result['score']}, 色调：{analysis_result['tone']}, 标签：{', '.join(analysis_result['tags'])}{Colors.END}")
                
                # 立即保存当前分析结果（使用增量保存）
                print(f"{Colors.BLUE}立即保存分析结果...{Colors.END}")
                if self.save_single_analysis_result(row):
                    print(f"{Colors.GREEN}✓ 保存成功{Colors.END}")
                else:
                    print(f"{Colors.RED}✗ 保存失败{Colors.END}")
                    
            else:
                error_count += 1
                # 设置默认值
                default_values = self.config["default_values"]
                row['评分'] = default_values["score"]
                row['色调'] = default_values["tone"]
                row['题材标签'] = ', '.join(default_values["tags"])
                
                # 即使是错误情况也保存，确保数据不丢失
                print(f"{Colors.RED}分析失败，保存默认值...{Colors.END}")
                if self.save_single_analysis_result(row):
                    print(f"{Colors.GREEN}✓ 默认值保存成功{Colors.END}")
                else:
                    print(f"{Colors.RED}✗ 默认值保存失败{Colors.END}")
            
            # 添加延迟避免过载
            time.sleep(self.delay)
        
        # 输出统计信息（不需要再次保存，因为已经实时保存了）
        print(f"\n{Colors.GREEN}{Colors.BOLD}分析完成统计：{Colors.END}")
        print(f"{Colors.GREEN}• 成功分析：{analyzed_count} 张{Colors.END}")
        print(f"{Colors.YELLOW}• 跳过：{skipped_count} 张{Colors.END}")
        print(f"{Colors.RED}• 错误：{error_count} 张{Colors.END}")
        print(f"{Colors.BLUE}• 总计：{len(data)} 张{Colors.END}")
        print(f"{Colors.GREEN}• 所有结果已实时保存到：{self.csv_output_path}{Colors.END}")
    
    def create_analysis_report(self) -> None:
        """创建分析报告"""
        if not os.path.exists(self.csv_output_path):
            print(f"{Colors.RED}未找到分析结果文件{Colors.END}")
            return
        
        try:
            # 读取分析结果
            data = []
            with open(self.csv_output_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('评分') and row['评分'] != '0':
                        data.append(row)
            
            if not data:
                print(f"{Colors.RED}没有有效的分析数据{Colors.END}")
                return
            
            # 统计分析
            scores = [int(row['评分']) for row in data if row['评分'].isdigit()]
            tones = [row['色调'] for row in data if row['色调'] and row['色调'] != '未知']
            
            # 找到评分最高的照片
            best_photos = []
            if scores:
                max_score = max(scores)
                candidates = []
                for row in data:
                    if row['评分'].isdigit() and int(row['评分']) == max_score:
                        candidates.append({
                            "文件名": row.get('文件名', ''),
                            "路径": row.get('路径', ''),
                            "评分": int(row['评分']),
                            "色调": row.get('色调', ''),
                            "题材标签": row.get('题材标签', ''),
                            "拍摄日期": row.get('拍摄日期', ''),
                            "时间": row.get('时间', ''),
                            "拍摄设备": row.get('拍摄设备', ''),
                            "镜头": row.get('镜头', ''),
                            "光圈": row.get('光圈', ''),
                            "快门": row.get('快门', ''),
                            "ISO": row.get('ISO', ''),
                            "等效35mm焦距": row.get('等效35mm焦距', '')
                        })
                
                # 如果有多张同分照片，使用LLM选择最佳
                if len(candidates) > 1:
                    print(f"{Colors.BLUE}发现 {len(candidates)} 张同分最高照片，进行AI深度比较选择最佳照片...{Colors.END}")
                    best_photo = self.select_best_photo_by_llm(candidates)
                    if best_photo:
                        best_photos = [best_photo]
                    else:
                        best_photos = [candidates[0]]  # 如果LLM选择失败，取第一张
                else:
                    best_photos = candidates
            
            # 统计题材标签
            all_tags = []
            for row in data:
                if row['题材标签'] and row['题材标签'] != '其他':
                    tags = [tag.strip() for tag in row['题材标签'].split(',')]
                    all_tags.extend(tags)
            
            # 生成报告
            report = {
                "总图片数": len(data),
                "平均评分": sum(scores) / len(scores) if scores else 0,
                "最高评分": max(scores) if scores else 0,
                "最低评分": min(scores) if scores else 0,
                "评分最佳照片": best_photos,
                "色调分布": {},
                "题材分布": {}
            }
            
            # 统计色调分布
            for tone in tones:
                report["色调分布"][tone] = report["色调分布"].get(tone, 0) + 1
            
            # 统计题材分布
            for tag in all_tags:
                report["题材分布"][tag] = report["题材分布"].get(tag, 0) + 1
            
            # 保存报告
            report_path = self.config["report_output_path"]
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"{Colors.GREEN}分析报告已保存到：{report_path}{Colors.END}")
            
            # 显示简要报告
            print(f"\n{Colors.BLUE}{Colors.BOLD}分析报告摘要：{Colors.END}")
            print(f"{Colors.BLUE}• 总图片数：{report['总图片数']}{Colors.END}")
            print(f"{Colors.BLUE}• 平均评分：{report['平均评分']:.1f}{Colors.END}")
            print(f"{Colors.BLUE}• 评分范围：{report['最低评分']} - {report['最高评分']}{Colors.END}")
            
            # 显示评分最佳照片
            if report['评分最佳照片']:
                print(f"\n{Colors.GREEN}{Colors.BOLD}评分最佳照片（{report['最高评分']}分）：{Colors.END}")
                for i, photo in enumerate(report['评分最佳照片'][:3], 1):  # 最多显示3张
                    print(f"{Colors.GREEN}  {i}. {photo['文件名']}{Colors.END}")
                    print(f"     色调：{photo['色调']}, 题材：{photo['题材标签']}")
                    print(f"     拍摄：{photo['拍摄日期']} {photo['时间']}")
                    if len(report['评分最佳照片']) > 1 and i < len(report['评分最佳照片']):
                        print()
            
            # 显示前5个色调
            print(f"\n{Colors.BLUE}主要色调：{Colors.END}")
            sorted_tones = sorted(report["色调分布"].items(), key=lambda x: x[1], reverse=True)[:5]
            for tone, count in sorted_tones:
                print(f"  {tone}: {count} 张")
            
            # 显示前5个题材
            print(f"\n{Colors.BLUE}主要题材：{Colors.END}")
            sorted_tags = sorted(report["题材分布"].items(), key=lambda x: x[1], reverse=True)[:5]
            for tag, count in sorted_tags:
                print(f"  {tag}: {count} 张")
                
        except Exception as e:
            print(f"{Colors.RED}生成分析报告错误：{str(e)}{Colors.END}")
    
    def test_llm_connection(self) -> bool:
        """测试LLM连接"""
        try:
            print(f"\n{Colors.BLUE}检查LLM服务连接...{Colors.END}")
            response = self.llm_api.simple_chat("你好，请回答：OK")
            if response and response.get('success'):
                print(f"{Colors.GREEN}LLM服务连接正常{Colors.END}")
                return True
            else:
                print(f"{Colors.RED}LLM服务不可用，请检查Ollama是否运行{Colors.END}")
                if response and response.get('error'):
                    print(f"{Colors.RED}错误详情：{response.get('error')}{Colors.END}")
                return False
        except Exception as e:
            print(f"{Colors.RED}LLM服务连接失败：{str(e)}{Colors.END}")
            return False
        
    def get_analysis_status(self, data: List[Dict]) -> Dict[str, int]:
        """获取分析状态统计"""
        analyzed = 0
        unanalyzed = 0
        
        for row in data:
            score = row.get('评分', '')
            tone = row.get('色调', '')
            tags = row.get('题材标签', '')
            
            # 检查是否已分析（与批量分析中的逻辑一致）
            if (score and str(score).strip() and str(score).strip() != '0' and 
                tone and str(tone).strip() and str(tone).strip() != '未知' and
                tags and str(tags).strip() and str(tags).strip() != '其他'):
                analyzed += 1
            else:
                unanalyzed += 1
        
        return {
            'total': len(data),
            'analyzed': analyzed,
            'unanalyzed': unanalyzed
        }
    
    def select_best_photo_by_llm(self, photos: List[Dict]) -> Optional[Dict]:
        """使用LLM从多张同分照片中选择最佳照片 - 仅基于图片内容分析"""
        if len(photos) <= 1:
            return photos[0] if photos else None
        
        try:
            print(f"{Colors.BLUE}发现 {len(photos)} 张同分最高照片，使用AI进行深度图片内容比较...{Colors.END}")
            
            # 构建纯图片内容比较提示词
            comparison_prompt = """请作为专业摄影评审，仔细观察并比较这些照片的内容质量。

请专注分析照片本身的视觉内容：
1. **构图美感**：画面构图的创意性、平衡感和视觉引导力
2. **色彩表现**：色彩搭配的和谐性、饱和度和情感传达
3. **光影效果**：光线运用的艺术性和立体感
4. **细节质量**：画面清晰度、层次感和细节丰富度
5. **视觉冲击力**：整体的艺术感染力和观赏价值
6. **情感表达**：画面传达的情感和故事性

请忽略技术参数，仅根据视觉内容判断哪张照片更优秀。

从以下照片中选择视觉质量最佳的一张：
"""
            
            # 添加照片基本信息（仅文件名）
            for i, photo in enumerate(photos, 1):
                comparison_prompt += f"\n照片 {i}：{photo['文件名']}"
            
            comparison_prompt += """

请直接回答选择的照片编号（1-{}），并简要说明选择理由（专注视觉内容，不超过50字）。
格式：选择照片X，理由：[基于视觉内容的理由]""".format(len(photos))
            
            # 逐张分析照片并进行比较
            best_photo = None
            best_score = -1
            comparison_results = []
            
            for i, photo in enumerate(photos):
                photo_path = photo.get('路径', '')
                if not os.path.exists(photo_path):
                    print(f"{Colors.YELLOW}跳过不存在的照片：{photo['文件名']}{Colors.END}")
                    continue
                
                print(f"{Colors.BLUE}  分析照片 {i+1}/{len(photos)}: {photo['文件名']}{Colors.END}")
                
                # 使用专门的比较提示词重新分析这张照片
                single_analysis_prompt = """请作为专业摄影评审，深度分析这张照片的视觉质量。

从以下维度给出详细评价：
1. 构图创意和视觉平衡（0-25分）
2. 色彩运用和和谐感（0-25分）  
3. 光影效果和立体感（0-25分）
4. 整体艺术感染力（0-25分）

请给出总分（0-100分）和简要评价。
格式：总分：X分，评价：[简要评价]"""
                
                try:
                    response = self.llm_api.analyze_image_with_text(
                        image_path=photo_path,
                        prompt=single_analysis_prompt
                    )
                    
                    if response and isinstance(response, str):
                        comparison_results.append({
                            'photo': photo,
                            'analysis': response,
                            'index': i
                        })
                        
                        # 尝试提取评分
                        response_lower = response.lower()
                        import re
                        score_match = re.search(r'总分[：:]\s*(\d+)', response_lower)
                        if score_match:
                            score = int(score_match.group(1))
                            if score > best_score:
                                best_score = score
                                best_photo = photo
                        
                except Exception as e:
                    print(f"{Colors.YELLOW}  分析照片 {photo['文件名']} 时出错：{str(e)}{Colors.END}")
                    continue
            
            # 如果成功找到最佳照片
            if best_photo:
                print(f"{Colors.GREEN}AI深度分析选择了最佳照片：{best_photo['文件名']} (评分: {best_score}){Colors.END}")
                
                # 显示比较结果
                print(f"{Colors.BLUE}详细比较结果：{Colors.END}")
                for result in comparison_results:
                    print(f"  {result['photo']['文件名']}: {result['analysis'][:100]}...")
                
                return best_photo
            else:
                print(f"{Colors.YELLOW}AI分析过程中出现问题，返回第一张照片{Colors.END}")
                return photos[0]
                
        except Exception as e:
            print(f"{Colors.RED}AI图片内容比较过程出错：{str(e)}，返回第一张照片{Colors.END}")
            return photos[0]

def main():
    """主函数"""
    print(f"{Colors.BLUE}{Colors.BOLD}╔════════════════════════════════════════════════╗{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.GREEN}图片内容分析器{Colors.BLUE}                          ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}使用AI深度分析图片质量、色调和题材{Colors.BLUE}        ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}╚════════════════════════════════════════════════╝{Colors.END}")
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='图片内容分析器')
    parser.add_argument('--start', type=int, default=0, help='开始索引（默认0）')
    parser.add_argument('--max', type=int, default=None, help='最大处理数量（默认全部）')
    parser.add_argument('--report', action='store_true', help='仅生成分析报告')
    
    args = parser.parse_args()
    
    analyzer = ContentAnalyzer()
    
    # 检查LLM服务
    if not analyzer.test_llm_connection():
        return
    
    if args.report:
        # 只生成报告
        analyzer.create_analysis_report()
    else:
        # 运行批量分析
        analyzer.run_batch_analysis(start_index=args.start, max_images=args.max)
        
        # 分析完成后生成报告
        print(f"\n{Colors.BLUE}生成分析报告...{Colors.END}")
        analyzer.create_analysis_report()


if __name__ == "__main__":
    main()
