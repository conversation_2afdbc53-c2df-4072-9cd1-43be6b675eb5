/* 简化版AI加载动画 */
.ai-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    padding: 10px 0;
    font-size: 0.9rem;
    border-left: 3px solid #4f46e5;
    padding-left: 10px;
}

/* 替换动画点为简单的静态加载指示器 */
.ai-loading-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #4f46e5;
    border-right-color: transparent;
    border-radius: 50%;
    animation: simple-rotate 1s linear infinite;
}

/* 简化的旋转动画，只有一个简单的旋转 */
@keyframes simple-rotate {
    to { transform: rotate(360deg); }
}

/* 结构化分析显示样式 */
.structured-analysis {
    margin-top: 15px;
    padding: 10px 0;
}

.analysis-section {
    margin-bottom: 15px;
}

.analysis-section h5 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.analysis-list {
    list-style: none;
    padding-left: 0;
}

.analysis-list li {
    position: relative;
    padding: 4px 0 4px 20px;
    margin-bottom: 6px;
    font-size: 0.95rem;
    line-height: 1.5;
    color: #444;
}

.analysis-list li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 10px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* 拍摄习惯项目标记 */
.analysis-section:first-child .analysis-list li:before {
    background-color: #4e73df;
}

/* 拍摄建议项目标记 */
.analysis-section:last-child .analysis-list li:before {
    background-color: #1cc88a;
}

/* 适应暗色主题 */
@media (prefers-color-scheme: dark) {
    .analysis-section h5 {
        color: #ddd;
        border-bottom-color: #444;
    }
    
    .analysis-list li {
        color: #bbb;
    }
}
