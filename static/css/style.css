/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    color: #212529;
    line-height: 1.6;
}

.container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 1rem;
}

/* 顶部导航 */
.navbar {
    background: #ffffff;
    padding: 0.8rem 0;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b7ff2;
}

.nav-links {
    display: flex;
    gap: 1.2rem;
    list-style: none;
}

.nav-links a {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.nav-links a:hover,
.nav-links a.active {
    background: #3b7ff2;
    color: white;
}

/* 卡片样式 - 保留用于其他页面 */
.card {
    background: #ffffff;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: #1e293b;
    font-weight: 700;
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* 旧版统计数据卡片样式 - 保留用于其他页面 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem 1.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.35);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.95rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* 主页布局优化 */
.main-charts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stats-info-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.stats-info-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stats-info-container .chart-title {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #2d3748;
    font-size: 1.3rem;
    font-weight: 800;
}

/* 图表容器 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
    margin-bottom: 0;
}

.chart-container {
    background: #ffffff;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-title {
    font-size: 1.2rem;
    margin-bottom: 1.2rem;
    color: #2d3748;
    font-weight: 700;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;
}

.chart-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.2rem;
}

.form-label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: 0.65rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #f8f9fa;
}

.form-input:focus {
    outline: none;
    border-color: #3b7ff2;
    box-shadow: 0 0 0 3px rgba(59, 127, 242, 0.15);
    background-color: #ffffff;
}

/* 按钮样式 */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6375 0%, #3f4755 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.25);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
    border: none;
}

.btn-info:hover {
    background-color: #138496;
}

/* 路径列表 */
.path-list {
    margin-bottom: 2rem;
}

.path-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.9);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid #e9ecef;
}

.path-text {
    font-family: 'Courier New', monospace;
    color: #495057;
    flex-grow: 1;
    margin-right: 1rem;
}

/* 消息提示 */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #cce7ff;
    border-color: #b8daff;
    color: #004085;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #856404;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 20px;
    margin: 2rem 0;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.empty-state-icon {
    margin-bottom: 2rem;
    color: #667eea;
    opacity: 0.7;
}

.empty-state-icon svg {
    width: 80px;
    height: 80px;
}

.empty-state-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.empty-state-description {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* 现代化仪表板样式 */
.modern-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    background: transparent;
}

/* 头部区域 */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
}

.header-content {
    max-width: 800px;
    margin: 0 auto;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.title-icon {
    font-size: 3rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.title-text {
    letter-spacing: -1px;
}

.dashboard-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
    line-height: 1.6;
    margin: 0;
}

/* 空状态样式 */
.empty-dashboard {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
}

.empty-content {
    text-align: center;
    max-width: 600px;
    background: white;
    padding: 4rem 3rem;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.empty-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    display: block;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.1));
}

.empty-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.empty-description {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.8;
    margin-bottom: 3rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* 现代化按钮 */
.btn-primary-large,
.btn-secondary-large {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.btn-primary-large {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary-large:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

.btn-secondary-large {
    background: white;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.btn-secondary-large:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-weight: 600;
}

/* 统计概览 */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card-modern {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
}

.stat-icon {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 16px;
    flex-shrink: 0;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 主要内容区域 */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

/* 左侧：最佳照片展示 */
.content-left {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.section-icon {
    font-size: 2rem;
}

.section-subtitle {
    font-size: 1rem;
    color: #64748b;
    margin: 0;
}

.best-photos-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.photo-card {
    background: #f8fafc;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.photo-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.photo-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.photo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-card:hover .photo-image img {
    transform: scale(1.05);
}

.photo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #64748b;
}

.placeholder-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.placeholder-text {
    font-size: 0.9rem;
    font-weight: 500;
}

.photo-score {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.photo-details {
    padding: 1.5rem;
}

.photo-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
    word-break: break-word;
}

.photo-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
}

.meta-icon {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.meta-text {
    color: #4a5568;
    font-weight: 500;
    flex: 1;
}

.view-photo-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-photo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

/* 右侧：信息面板 */
.content-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-panel {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.info-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
}

.panel-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
}

.panel-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-icon {
    font-size: 1.2rem;
}

.panel-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-label {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    word-break: break-word;
}

.info-count {
    font-size: 0.85rem;
    color: #667eea;
    font-weight: 500;
}

/* 图表仪表板 */
.charts-dashboard {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.charts-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f1f5f9;
}

.charts-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.charts-icon {
    font-size: 2.2rem;
}

.charts-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin: 0;
}

.charts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.chart-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: #f8fafc;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.chart-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.chart-description {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.chart-content {
    height: 300px;
    border-radius: 12px;
    overflow: hidden;
}

.chart-content.wordcloud {
    background: white;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 操作按钮区域 */
.dashboard-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 16px;
    margin-bottom: 2rem;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
    justify-content: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
    background: #64748b;
    color: white;
    box-shadow: 0 4px 16px rgba(100, 116, 139, 0.3);
}

.action-btn.secondary:hover {
    background: #475569;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(100, 116, 139, 0.4);
}

.action-btn.outline {
    background: white;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.action-btn.outline:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    transform: translateY(-2px);
}

/* 全屏照片查看器 */
.photo-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.photo-viewer-overlay.active {
    opacity: 1;
    visibility: visible;
}

.photo-viewer-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.photo-viewer-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 1.5rem;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.photo-viewer-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.photo-viewer-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.photo-viewer-title {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    padding: 1rem 2rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .content-left {
        order: 2;
    }
    
    .content-right {
        order: 1;
    }
    
    .chart-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .modern-dashboard {
        padding: 0 1rem;
    }
    
    .dashboard-header {
        padding: 2rem 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .dashboard-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .title-icon {
        font-size: 2.5rem;
    }
    
    .dashboard-subtitle {
        font-size: 1rem;
    }
    
    .stats-overview {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card-modern {
        padding: 1.5rem;
    }
    
    .stat-icon {
        font-size: 2rem;
        width: 60px;
        height: 60px;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .empty-content {
        padding: 2.5rem 2rem;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary-large,
    .btn-secondary-large {
        width: 100%;
        max-width: 280px;
    }
    
    .best-photos-container {
        grid-template-columns: 1fr;
    }
    
    .content-left,
    .info-panel {
        padding: 1.5rem;
    }
    
    .charts-dashboard {
        padding: 1.5rem;
    }
    
    .chart-section {
        grid-template-columns: 1fr;
    }
    
    .chart-content {
        height: 250px;
    }
    
    .dashboard-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .action-btn {
        width: 100%;
        max-width: 280px;
    }
    
    .photo-viewer-content {
        max-width: 95%;
        max-height: 95%;
    }
    
    .photo-viewer-close {
        top: -40px;
        right: -10px;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 1.5rem 1rem;
    }
    
    .dashboard-title {
        font-size: 1.6rem;
    }
    
    .title-icon {
        font-size: 2rem;
    }
    
    .dashboard-subtitle {
        font-size: 0.9rem;
    }
    
    .stat-card-modern {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem 1rem;
    }
    
    .stat-icon {
        margin-bottom: 1rem;
    }
    
    .empty-content {
        padding: 2rem 1.5rem;
    }
    
    .empty-icon {
        font-size: 4rem;
    }
    
    .empty-title {
        font-size: 1.6rem;
    }
    
    .photo-image {
        height: 160px;
    }
    
    .photo-details {
        padding: 1rem;
    }
    
    .content-left,
    .info-panel,
    .charts-dashboard {
        padding: 1rem;
    }
    
    .chart-content {
        height: 220px;
    }
}

.info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 16px 16px 0 0;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.2);
}

.info-card-header {
    margin-bottom: 1rem;
}

.info-card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #334155;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-card-header h3::before {
    content: '';
    width: 3px;
    height: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.info-card-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat-row {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stat-row .stat-label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0;
}

.stat-row .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    word-break: break-all;
}

.stat-row .stat-count {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 500;
}

/* 图表区域样式 */
.charts-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.08);
}

.charts-section h3.section-title {
    text-align: center;
    margin-bottom: 2rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
}

.chart-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-header h3,
.chart-header h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
    font-weight: 400;
}

.chart-content {
    height: 350px;
    border-radius: 8px;
    overflow: hidden;
}

/* 操作按钮区域 */
.actions-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 16px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.actions-container .btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
    justify-content: center;
}

.actions-container .btn:hover {
    transform: translateY(-3px);
}

/* 路径选择相关样式 */
.form-help {
    color: #666;
    font-size: 0.9em;
    margin-top: 0.5rem;
    display: block;
}

.btn-outline {
    background: transparent;
    border: 1px solid #ddd;
    color: #666;
    transition: all 0.2s ease;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #999;
    color: #333;
}

.path-selection-info {
    background: #e8f4f8;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.path-selection-info code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #d0d7de;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafbfc;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: #3b7ff2;
    background-color: #f6f8fa;
}

.upload-area.dragover {
    border-color: #3b7ff2;
    background-color: #e6f3ff;
    transform: scale(1.02);
}

.upload-area {
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(59, 127, 242, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-area.dragover::before {
    opacity: 1;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.upload-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.upload-hint {
    color: #6c757d;
    margin: 0;
}

/* 上传选项按钮样式 */
.upload-options {
    margin-bottom: 1rem;
}

.upload-options .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.upload-options .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b7ff2, #28a745);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    color: #495057;
    font-weight: 500;
    margin: 0;
}

/* 文件列表样式 */
.file-list {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-icon {
    font-size: 1.2rem;
}

.file-name {
    font-weight: 500;
    color: #495057;
}

.file-size {
    font-size: 0.9rem;
    color: #6c757d;
}

.file-status {
    font-size: 0.9rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

.file-status.ready {
    background-color: #d1ecf1;
    color: #0c5460;
}

.file-status.uploading {
    background-color: #fff3cd;
    color: #856404;
}

.file-status.success {
    background-color: #d4edda;
    color: #155724;
}

.file-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 文件夹信息显示 */
.folder-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #1565c0;
}

/* 设备镜头统计样式 */
.device-lens-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 0;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 24px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.stat-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
}

.stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.stat-label {
    font-weight: 700;
    color: #2d3748;
    font-size: 0.95rem;
    min-width: 120px;
    text-transform: none;
    letter-spacing: 0.3px;
}

.stat-value {
    font-weight: 600;
    color: #4a5568;
    font-size: 1rem;
    flex: 1;
    margin-left: 16px;
    text-align: left;
    word-break: break-word;
}

.stat-count {
    font-size: 0.85rem;
    color: #667eea;
    font-weight: 700;
    background: rgba(102, 126, 234, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    margin-left: 8px;
    white-space: nowrap;
}

/* 现代化加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(248, 250, 252, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 1.1rem;
    color: #64748b;
    font-weight: 500;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .path-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .path-text {
        margin-right: 0;
        word-break: break-all;
    }
    
    .upload-area {
        padding: 1.5rem;
        min-height: 120px;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .upload-text {
        font-size: 1rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .device-lens-stats {
        gap: 10px;
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px;
    }
    
    .stat-label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .stat-value {
        margin-left: 0;
    }
    
    /* 最佳照片区域响应式 */
    .best-photo-section {
        padding: 1.5rem;
    }
    
    .best-photos-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .photo-info {
        padding: 1rem;
    }
    
    .analysis-section {
        padding: 1.5rem;
    }
    
    .analysis-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .analysis-card {
        padding: 1rem;
    }
    
    .wordcloud-container {
        height: 250px;
    }
    
    .photo-viewer-content {
        max-width: 95%;
        max-height: 95%;
    }
    
    .photo-viewer-close {
        top: -35px;
        right: -5px;
    }
    
    .photo-viewer-image {
        max-height: 70vh;
    }
    
    .actions-container {
        flex-direction: column;
        gap: 0.75rem;
        padding: 1.5rem;
    }
    
    .actions-container .btn {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .info-cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .chart-content {
        height: 280px;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .empty-state {
        padding: 2.5rem 1.5rem;
    }
    
    .empty-state-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .empty-state-actions .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .charts-section {
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .chart-content {
        height: 250px;
    }
    
    .info-card {
        padding: 1.25rem;
    }
    
    .best-photo-section {
        padding: 1rem;
        border-radius: 16px;
    }
    
    .best-photos-grid {
        gap: 0.75rem;
    }
    
    .photo-preview {
        height: 160px;
    }
    
    .section-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }
    
    .meta-row {
        font-size: 0.8rem;
    }
    
    .photo-score-badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .score-number {
        font-size: 1.1rem;
    }
    
    .photo-viewer-title {
        font-size: 1rem;
        padding: 0.4rem 0.8rem;
    }
}
