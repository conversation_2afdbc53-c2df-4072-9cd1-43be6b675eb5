/* 简约界面样式 - 由GitHub Copilot创建 */

/* 基础样式 */
:root {
  --primary: #3498db;
  --primary-light: #5dade2;
  --primary-dark: #2980b9;
  --secondary: #2ecc71;
  --secondary-light: #58d68d;
  --secondary-dark: #27ae60;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --background: #f5f7fa;
  --card-bg: #ffffff;
  --border: #ecf0f1;
  --shadow: rgba(0,0,0,0.05);
  --radius: 12px;
  --transition: all 0.3s ease;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* 简约导航栏 */
.navbar-minimal {
  background-color: var(--card-bg);
  box-shadow: 0 2px 8px var(--shadow);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container-minimal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0.75rem 1.5rem;
}

.logo-minimal {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--primary);
  text-decoration: none;
  gap: 0.5rem;
}

.logo-minimal .logo-icon {
  font-size: 1.4rem;
}

.nav-links-minimal {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1rem;
}

.nav-links-minimal li a {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius);
  transition: var(--transition);
  font-weight: 500;
  gap: 0.35rem;
}

.nav-links-minimal li a:hover {
  background-color: rgba(52, 152, 219, 0.1);
  color: var(--primary);
}

.nav-links-minimal li a.active {
  color: var(--primary);
  background-color: rgba(52, 152, 219, 0.1);
}

/* 简约仪表盘 */
.dashboard-minimal {
  max-width: 1280px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.dashboard-welcome {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: 0 4px 12px var(--shadow);
}

.welcome-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.welcome-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  padding: 1.25rem;
  box-shadow: 0 2px 8px var(--shadow);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.stat-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary);
  border-radius: 50%;
}

.stat-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-top: 0.25rem;
}

/* 内容区域 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 992px) {
  .content-grid {
    grid-template-columns: 2fr 1fr;
  }
}

.content-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px var(--shadow);
  overflow: hidden;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  gap: 0.5rem;
}

.card-icon {
  font-size: 1.25rem;
  color: var(--primary);
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.card-content {
  padding: 1.5rem;
}

/* 照片卡片 */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
}

.photo-card {
  border-radius: var(--radius);
  overflow: hidden;
  background: var(--card-bg);
  box-shadow: 0 2px 8px var(--shadow);
  transition: var(--transition);
}

.photo-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px var(--shadow);
}

.photo-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 66.67%;
  overflow: hidden;
}

.photo-image img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-card:hover .photo-image img {
  transform: scale(1.05);
}

.photo-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: var(--primary);
  color: white;
  border-radius: 20px;
  padding: 0.25rem 0.75rem;
  font-weight: 600;
  font-size: 0.8rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.photo-info {
  padding: 1rem;
}

.photo-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-meta {
  display: flex;
  flex-direction: column;
  gap: 0.35rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 0.35rem;
}

.meta-icon {
  font-size: 0.9rem;
}

/* 信息面板 */
.info-panel {
  padding: 1rem;
  border-bottom: 1px solid var(--border);
}

.info-panel:last-child {
  border-bottom: none;
}

.info-label {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  gap: 0.35rem;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
}

/* 按钮样式 */
.btn-minimal {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary-minimal {
  background-color: var(--primary);
  color: white;
}

.btn-primary-minimal:hover {
  background-color: var(--primary-dark);
}

.btn-secondary-minimal {
  background-color: rgba(52, 152, 219, 0.1);
  color: var(--primary);
}

.btn-secondary-minimal:hover {
  background-color: rgba(52, 152, 219, 0.2);
}

/* 空状态 */
.empty-state-minimal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.empty-icon-minimal {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--primary);
  opacity: 0.7;
}

.empty-title-minimal {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.empty-text-minimal {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  max-width: 450px;
}

.empty-actions-minimal {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .nav-container-minimal {
    flex-direction: column;
    padding: 0.75rem;
  }
  
  .logo-minimal {
    margin-bottom: 0.75rem;
  }
  
  .nav-links-minimal {
    width: 100%;
    justify-content: space-around;
    gap: 0.5rem;
  }
  
  .nav-links-minimal li a {
    padding: 0.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
