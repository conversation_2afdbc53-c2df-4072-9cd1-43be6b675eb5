/* 现代化界面的额外样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* 美化空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 1rem;
    margin: 2rem auto;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.empty-state-icon {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
}

.empty-state-title {
    font-size: 1.8rem;
    margin-bottom: 1.2rem;
    color: #2d3748;
    font-weight: 700;
}

.empty-state-description {
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

/* 添加平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 美化图表容器 */
.chart-container {
    position: relative;
    transition: all 0.2s ease;
}

.chart-container:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 导航链接增强 */
.nav-links a span {
    margin-right: 0.3rem;
    opacity: 0.8;
}

/* 按钮增强 */
.btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 12px;
    padding: 12px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(102, 126, 234, 0.3);
    color: #4a5568;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-2px);
}

/* 按钮脉冲效果 */
.btn-primary:focus {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* 新设置界面样式 */

/* 主设置菜单 */
.settings-main-menu {
    padding: 1rem 0;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.settings-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.settings-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.settings-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.settings-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.settings-card p {
    opacity: 0.9;
    line-height: 1.5;
    margin: 0;
}

/* 设置页面 */
.settings-page {
    animation: fadeIn 0.3s ease-in-out;
}

.settings-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.back-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #5a6fd8;
    transform: translateX(-2px);
}

/* 系统信息网格 */
.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.info-icon {
    font-size: 2rem;
    color: #667eea;
}

.info-content h4 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-size: 1rem;
}

.info-content p {
    margin: 0;
    color: #4a5568;
    font-size: 0.9rem;
}

/* 运行统计 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 操作网格 */
.operation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.operation-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.operation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.operation-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.operation-card h4 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-size: 1.1rem;
}

.operation-card p {
    margin: 0 0 1rem 0;
    color: #4a5568;
    font-size: 0.9rem;
}

.operation-card .btn {
    width: 100%;
}

/* 系统维护操作 */
.maintenance-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.maintenance-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* 快捷操作 */
.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.quick-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* 路径列表样式 */
.path-list {
    margin-top: 1rem;
}

.path-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.path-text {
    flex: 1;
    margin-right: 1rem;
    font-family: 'Courier New', monospace;
    color: #2d3748;
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #667eea;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    margin: 1rem 0;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #5a6fd8;
    background: #f0f4ff;
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.upload-text {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #4a5568;
    font-size: 0.9rem;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    color: #4a5568;
    font-size: 0.9rem;
    margin: 0;
}

/* 警告框样式 */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.alert-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    color: #0d47a1;
}

.alert-warning {
    background: #fff3e0;
    border: 1px solid #ffcc02;
    color: #e65100;
}

/* 开关切换按钮样式 */
.toggle-switch {
    position: relative;
    width: 60px;
    height: 34px;
    margin: 10px 0;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #667eea;
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* 分析结果卡片 */
.analysis-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.97) 0%, rgba(249,250,251,0.97) 100%);
    border-radius: 10px;
    padding: 16px 18px;
    margin-top: 16px;
    border-left: 3px solid #4f46e5;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    animation: fade-in 0.6s ease-out;
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(-8px); }
    to { opacity: 1; transform: translateY(0); }
}

.analysis-card h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 10px 0;
    color: #4f46e5;
    font-size: 0.95rem;
    font-weight: 600;
}

.analysis-card p {
    margin: 0;
    color: #1f2937;
    line-height: 1.6;
    font-size: 0.95rem;
}

.analysis-card .analysis-text {
    white-space: pre-line; /* 保留换行符 */
}

/* 要点项 */
.analysis-card .analysis-text ul {
    margin: 0;
    padding-left: 20px;
}

.analysis-card .analysis-text li {
    margin-bottom: 4px;
}

/* 刷新分析按钮 */
.btn-refresh {
    font-size: 0.85rem;
    padding: 4px 8px;
    margin-top: 12px;
    opacity: 0.8;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    color: #475569;
    border-radius: 6px;
}

.btn-refresh:hover {
    background-color: #f1f5f9;
    opacity: 1;
}

/* AI回答加载动画 */
.ai-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    padding: 10px 0;
    font-size: 0.9rem;
}

.ai-loading-dots {
    display: inline-block;
    margin-left: 5px;
}

/* 简化为单个静态指示器 */
.ai-loading-dots span {
    display: none;
}

/* 最佳照片展示样式 */
.best-photo-section {
    margin: 2rem 0;
}

.best-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.best-photo-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: all 0.3s ease;
}

.best-photo-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.photo-preview {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.photo-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.best-photo-card:hover .photo-thumbnail {
    transform: scale(1.05);
}

.photo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
}

.placeholder-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

.photo-info {
    padding: 1.5rem;
}

.photo-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.photo-score-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.score-number {
    font-size: 1.1rem;
    margin-right: 0.25rem;
}

.score-label {
    opacity: 0.9;
}

.photo-meta {
    margin: 1rem 0;
}

.meta-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.meta-label {
    width: 1.5rem;
    text-align: center;
    margin-right: 0.75rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.meta-value {
    flex: 1;
    font-size: 0.9rem;
    color: #4a5568;
    font-weight: 500;
}

.photo-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn-view-full {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
}

.btn-view-full:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

/* 全屏照片查看器 */
.photo-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.photo-viewer-overlay.active {
    opacity: 1;
    visibility: visible;
}

.photo-viewer-content {
    max-width: 90vw;
    max-height: 90vh;
    position: relative;
}

.photo-viewer-image {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.photo-viewer-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-viewer-close:hover {
    background: white;
    transform: scale(1.1);
}

.photo-viewer-title {
    position: absolute;
    bottom: -40px;
    left: 0;
    color: white;
    font-size: 1rem;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-charts-container {
        gap: 1.5rem;
    }
    
    .stats-info-container {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .device-lens-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.5rem 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .chart-container {
        padding: 1.2rem;
        border-radius: 12px;
    }
    
    .chart-title {
        font-size: 1.1rem;
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
        padding: 15px 18px;
    }
    
    .stat-value {
        margin-left: 0;
        margin-top: 8px;
        font-size: 0.95rem;
    }
    
    .stat-count {
        margin-left: 0;
        margin-top: 6px;
        align-self: flex-start;
        font-size: 0.8rem;
    }
    
    .logo {
        font-size: 1.2rem;
    }
    
    .nav-links {
        gap: 0.6rem;
    }
    
    .nav-links a {
        padding: 0.3rem 0.5rem;
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.85rem;
        margin: 0.25rem;
    }
    
    .card {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .card-title {
        font-size: 1.3rem;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .system-info-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .operation-grid {
        grid-template-columns: 1fr;
    }
    
    .maintenance-actions {
        flex-direction: column;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .path-item {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .path-text {
        margin-right: 0;
        word-break: break-all;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }
    
    .stat-card {
        padding: 1.2rem 0.8rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .empty-state {
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 4rem;
    }
    
    .empty-state-title {
        font-size: 1.5rem;
    }
}

/* 美化加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 2rem;
}

.loading-spinner {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 3px solid #3b7ff2;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
