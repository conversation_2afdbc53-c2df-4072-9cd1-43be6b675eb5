/* 优化后的照片分析器样式 - 简约、美观、对齐 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* 导航栏 */
.navbar {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
}

.nav-links a {
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav-links a:hover,
.nav-links a.active {
    background: #2563eb;
    color: white;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 主要内容区域 */
.main-content-area {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* 信息卡片容器 */
.info-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.info-card-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat-row {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stat-row .stat-label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.stat-row .stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    word-break: break-word;
}

.stat-row .stat-count {
    font-size: 0.85rem;
    color: #2563eb;
    font-weight: 500;
}

/* 图表区域 */
.charts-section {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
}

.chart-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.chart-content {
    height: 350px;
    background: white;
    border-radius: 4px;
    padding: 0.5rem;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: #2563eb;
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
}

.btn-secondary {
    background: #64748b;
    color: white;
}

.btn-secondary:hover {
    background: #475569;
}

.btn-outline {
    background: white;
    color: #2563eb;
    border: 1px solid #2563eb;
}

.btn-outline:hover {
    background: #2563eb;
    color: white;
}

.btn-danger {
    background: #dc2626;
    color: white;
}

.btn-danger:hover {
    background: #b91c1c;
}

/* 操作按钮区域 */
.actions-container {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin: 2rem 0;
}

.empty-state-icon {
    font-size: 4rem;
    color: #64748b;
    margin-bottom: 1.5rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.empty-state-description {
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 路径列表 */
.path-list {
    margin-bottom: 2rem;
}

.path-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.path-text {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: #374151;
    flex: 1;
    margin-right: 1rem;
    word-break: break-all;
}

/* 设备统计 */
.device-lens-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    border-left: 4px solid #2563eb;
}

.stat-item .stat-label {
    font-weight: 600;
    color: #1e293b;
    min-width: 120px;
}

.stat-item .stat-value {
    flex: 1;
    margin-left: 1rem;
    color: #374151;
    word-break: break-word;
}

.stat-item .stat-count {
    background: #eff6ff;
    color: #2563eb;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示 */
.alert {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-danger {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-info {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #d97706;
}

/* 设置页面样式 */
.settings-menu {
    margin-bottom: 2rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.settings-card {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.settings-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.settings-card h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.settings-card p {
    opacity: 0.9;
    margin: 0;
}

.settings-page {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.settings-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.back-btn {
    background: #64748b;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.back-btn:hover {
    background: #475569;
}

.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    border-left: 4px solid #2563eb;
}

.info-icon {
    font-size: 1.5rem;
    color: #2563eb;
}

.info-content h4 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1rem;
}

.info-content p {
    margin: 0;
    color: #64748b;
    font-size: 0.9rem;
}

.operation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.operation-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 1.5rem;
    text-align: center;
}

.operation-icon {
    font-size: 2rem;
    color: #2563eb;
    margin-bottom: 1rem;
}

.operation-card h4 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.1rem;
}

.operation-card p {
    margin: 0 0 1rem 0;
    color: #64748b;
    font-size: 0.9rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.form-help {
    color: #64748b;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
}

/* 最佳照片展示 */
.best-photo-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #334155;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.best-photo-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.photo-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.photo-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.photo-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    background: #ecfdf5;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    display: inline-block;
    width: fit-content;
}

.photo-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.detail-label {
    font-weight: 500;
    color: #64748b;
}

.detail-value {
    font-weight: 600;
    color: #1e293b;
    text-align: right;
}

/* 分析概览区域 */
.analysis-section {
    margin-bottom: 2rem;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.analysis-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.wordcloud-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    border-radius: 8px;
    position: relative;
}

/* 扩展统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .info-cards-container {
        grid-template-columns: 1fr;
    }
    
    .actions-container {
        flex-direction: column;
    }
    
    .path-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .path-text {
        margin-right: 0;
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .stat-item .stat-value {
        margin-left: 0;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .system-info-grid {
        grid-template-columns: 1fr;
    }
    
    .operation-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .settings-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .chart-content {
        height: 300px;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
    
    .best-photo-card {
        padding: 1rem;
    }
    
    .analysis-card {
        padding: 1rem;
    }
    
    .wordcloud-container {
        height: 250px;
    }
}
