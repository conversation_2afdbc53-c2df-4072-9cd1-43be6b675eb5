/* 选择性动画样式覆盖 - 仅保留AI分析圆点动画 */

/* 禁用全局过渡效果，但排除AI分析相关类 */
*:not(.ai-loading):not(.ai-loading-dots):not(.ai-loading-dots span) {
    transition: none !important;
}

*:not(.ai-loading-dots):not(.ai-loading-dots span):not(.spinner) {
    animation: none !important;
}

/* 禁用加载遮罩的动画和效果 */
.loading-overlay {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* 允许AI分析加载动画 */
.ai-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    padding: 10px 0;
    font-size: 0.9rem;
    border-left: 3px solid #4f46e5;
    padding-left: 10px;
}

.ai-loading-dots {
    display: flex;
    gap: 4px;
}

.ai-loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #4f46e5;
    animation: ai-dot-pulse 1.4s infinite ease-in-out both;
}

.ai-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.ai-loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes ai-dot-pulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* 禁用图表过渡动画 */
.js-plotly-plot .plotly .g-gtitle,
.js-plotly-plot .plotly .g-xtitle,
.js-plotly-plot .plotly .g-ytitle {
    transform: none !important;
    transition: none !important;
}

/* 禁用图表元素动画 */
.js-plotly-plot .plotly .trace,
.js-plotly-plot .plotly .points,
.js-plotly-plot .plotly .bars,
.js-plotly-plot .plotly .scattergl,
.js-plotly-plot .plotly .scatterpolargl {
    transition: none !important;
}

/* 禁用各种UI元素的过渡效果 */
.card, 
.button, 
.nav-link, 
.modal, 
.dropdown-menu,
.notification,
.btn, 
button,
input,
select,
.tab,
.path-item,
.file-item {
    transition: none !important;
    animation: none !important;
}

/* 页面切换和内容更新无动画 */
.content,
.main-content,
.section,
.tab-content {
    transition: none !important;
    animation: none !important;
    opacity: 1 !important;
}
