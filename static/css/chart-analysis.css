/* 优化AI分析卡片样式 */
.chart-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-content {
    width: 100%;
    height: 300px;
    position: relative;
}

.analysis-card {
    width: 100%;
    padding: 20px;
    border-radius: 16px;
    background: rgba(250, 251, 254, 0.85);
    border: 1px solid rgba(226, 232, 240, 0.9);
    box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    margin-top: 15px;
    backdrop-filter: blur(8px);
    animation: card-fade-in 0.6s ease-out;
}

@keyframes card-fade-in {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.analysis-card:hover {
    box-shadow: 0 14px 20px -6px rgba(0, 0, 0, 0.12);
    transform: translateY(-3px);
    border-color: rgba(99, 102, 241, 0.3);
}

.analysis-card h4 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #334155;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.7);
}

.analysis-card h4::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -1px;
    height: 2px;
    width: 60px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 2px;
}

.analysis-card h4 span {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.analysis-content {
    font-size: 0.95rem;
}

.analysis-summary {
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 1rem;
    border-left: 3px solid #3b82f6;
    padding: 0.75rem 1rem;
    line-height: 1.5;
    background-color: rgba(99, 102, 241, 0.05);
    border-radius: 0.5rem;
    font-style: italic;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    animation: fade-in 0.8s ease;
}

@keyframes fade-in {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

.analysis-summary::before {
    content: '"';
    position: absolute;
    left: 0.5rem;
    top: -0.5rem;
    font-size: 1.8rem;
    color: rgba(99, 102, 241, 0.3);
    font-family: serif;
}

.analysis-summary::after {
    content: '"';
    position: absolute;
    right: 0.5rem;
    bottom: -1rem;
    font-size: 1.8rem;
    color: rgba(99, 102, 241, 0.3);
    font-family: serif;
}

.analysis-card p {
    margin: 0;
    color: #1f2937;
    line-height: 1.6;
    font-size: 0.95rem;
}

.analysis-card .analysis-text {
    white-space: pre-line; /* 保留换行符 */
}

/* 分析内容各部分样式 */
.analysis-section {
    margin-bottom: 1.5rem;
    background-color: rgba(255, 255, 255, 0.6);
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid #6366f1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    animation: slide-in 0.5s ease-out;
}

@keyframes slide-in {
    0% { transform: translateX(-10px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

.analysis-section:hover {
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.suggestions-section {
    margin-bottom: 1.5rem;
    background-color: rgba(236, 253, 245, 0.6);
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid #10b981;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    animation: slide-in 0.5s ease-out;
    animation-delay: 0.1s;
}

.suggestions-section:hover {
    background-color: rgba(236, 253, 245, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.analysis-section h5,
.suggestions-section h5 {
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
    margin: 0 0 0.8rem 0;
    display: inline-block;
    position: relative;
}

.analysis-section h5::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    height: 2px;
    width: 100%;
    background: linear-gradient(90deg, #6366f1, rgba(99, 102, 241, 0.2));
    border-radius: 2px;
}

.suggestions-section h5::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    height: 2px;
    width: 100%;
    background: linear-gradient(90deg, #10b981, rgba(16, 185, 129, 0.2));
    border-radius: 2px;
}

/* 要点列表 */
.insights-list,
.suggestions-list {
    list-style: none;
    margin: 0.8rem 0 0 0;
    padding: 0;
}

.insights-list li,
.suggestions-list li {
    margin-bottom: 0.7rem;
    padding-left: 1.8rem;
    position: relative;
    line-height: 1.5;
}

.insights-list li::before {
    content: "✦";
    position: absolute;
    left: 0;
    color: #6366f1;
    font-weight: bold;
}

.suggestions-list li::before {
    content: "✧";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

/* 兼容旧格式 */
.analysis-card .analysis-text ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.analysis-card .analysis-text li {
    margin-bottom: 0.6rem;
    color: #334155;
    line-height: 1.5;
}

/* 刷新分析按钮 */
.btn-refresh {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    margin-top: 1rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    color: #475569;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.btn-refresh:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.btn-refresh:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* AI回答加载动画 */
.ai-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    padding: 10px 0;
    font-size: 0.9rem;
}

.ai-loading-dots {
    display: flex;
    gap: 4px;
}

.ai-loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #4f46e5;
    animation: ai-dot-pulse 1.4s infinite ease-in-out both;
}

.ai-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.ai-loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes ai-dot-pulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* 设置网格布局以避免重叠 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    width: 100%;
}

/* 更大的响应式设置 */
@media (min-width: 768px) {
    .chart-content {
        height: 350px;
    }
}

@media (min-width: 1200px) {
    .chart-content {
        height: 400px;
    }
}

/* 响应式调整，防止小屏幕上的重叠 */
@media (max-width: 768px) {
    .chart-container {
        margin-bottom: 40px;
    }
    
    .chart-content {
        height: 280px;
    }
    
    .analysis-card {
        margin-top: 20px;
    }
}

/* 结构化分析结果样式 */
.insights-list {
    list-style: none;
    padding-left: 0;
    margin: 0.5rem 0;
}

.insights-list li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    color: #334155;
}

.insights-list li:before {
    content: "•";
    position: absolute;
    left: 0.5rem;
    color: #6366f1;
    font-size: 1.2rem;
    line-height: 1;
}

.suggestions-section {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px dashed #e2e8f0;
}

.suggestions-section h5 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #334155;
    margin-top: 0;
    margin-bottom: 0.5rem;
}

.suggestions-list {
    list-style: none;
    padding-left: 0;
    margin: 0.5rem 0;
}

.suggestions-list li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #475569;
}

.suggestions-list li:before {
    content: "↪";
    position: absolute;
    left: 0.25rem;
    color: #64748b;
}

/* 缓存指示器和分析操作容器样式 */
.analysis-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px dashed rgba(226, 232, 240, 0.8);
}

.cache-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background-color: rgba(226, 232, 240, 0.4);
    color: #64748b;
    border-radius: 6px;
    font-size: 0.75rem;
    animation: fade-in 1s ease;
}
