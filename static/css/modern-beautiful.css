/* 现代化美观界面样式 - 由GitHub Copilot优化设计 */

/* 现代化配色方案 */
:root {
  /* 主色调 - 深蓝渐变 */
  --primary: #4568dc;
  --primary-light: #6a8bef;
  --primary-dark: #3451b2;
  --primary-gradient: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
  
  /* 辅助色调 */
  --secondary: #38ef7d;
  --secondary-light: #57f59c;
  --secondary-dark: #2ece6d;
  --secondary-gradient: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
  
  /* 中性色调 */
  --text-primary: #2c3e50;
  --text-secondary: #5d6c7b;
  --text-light: #8395a7;
  --text-white: #ffffff;
  
  /* 背景色调 */
  --background: #f8faff;
  --background-gradient: linear-gradient(135deg, #f6f9fc 0%, #eef2fa 100%);
  --card-bg: #ffffff;
  --card-bg-alt: #f8faff;
  
  /* 装饰元素 */
  --border: rgba(221, 226, 236, 0.7);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 30px rgba(70, 104, 220, 0.12);
  --shadow-inset: inset 0 2px 5px rgba(0, 0, 0, 0.03);
  
  /* 圆角与过渡 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 20px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 评分颜色 */
  --rating-high: #27ae60;
  --rating-medium: #f39c12;
  --rating-low: #e74c3c;
}

/* 全局样式优化 */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background-gradient);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* 现代化导航栏 */
.navbar-modern {
  background-color: var(--card-bg);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border);
}

.nav-container-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0.9rem 1.5rem;
}

.logo-modern {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.3rem;
  position: relative;
  padding-left: 1.5rem;
}

.logo-modern::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--primary-gradient);
  border-radius: 4px;
}

.logo-icon {
  font-size: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-links-modern {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}

.nav-links-modern li a {
  display: flex;
  align-items: center;
  padding: 0.6rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: var(--transition);
  font-weight: 500;
  font-size: 0.95rem;
  gap: 0.5rem;
}

.nav-links-modern li a:hover {
  background-color: rgba(69, 104, 220, 0.08);
  color: var(--primary);
}

.nav-links-modern li a.active {
  background-color: rgba(69, 104, 220, 0.1);
  color: var(--primary);
  font-weight: 600;
}

.nav-icon {
  display: inline-block;
  font-size: 1.2rem;
}

/* 主仪表板布局 */
.dashboard-modern {
  max-width: 1280px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

/* 欢迎卡片 */
.welcome-card {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border);
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: var(--primary-gradient);
}

.welcome-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* 统计卡片网格 */
.stats-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.stat-card-modern {
  background: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border);
  display: flex;
  flex-direction: column;
}

.stat-card-modern:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.stat-icon-wrapper {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(69, 104, 220, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.stat-icon-modern {
  font-size: 1.6rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label-modern {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.stat-value-modern {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-light);
}

.trend-up {
  color: var(--rating-high);
}

.trend-down {
  color: var(--rating-low);
}

/* 内容卡片 */
.content-grid-modern {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (min-width: 992px) {
  .content-grid-modern {
    grid-template-columns: 2fr 1fr;
  }
}

.content-card-modern {
  background: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid var(--border);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header-modern {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border);
  gap: 0.75rem;
}

.card-icon-modern {
  font-size: 1.35rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card-title-modern {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  flex-grow: 1;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.card-action-btn {
  background: none;
  border: none;
  font-size: 1rem;
  color: var(--text-light);
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.card-action-btn:hover {
  background-color: rgba(69, 104, 220, 0.08);
  color: var(--primary);
}

.card-content-modern {
  padding: 1.5rem;
  flex-grow: 1;
}

/* 照片网格 */
.photos-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.25rem;
}

.photo-card-modern {
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--card-bg-alt);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border: 1px solid var(--border);
}

.photo-card-modern:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.photo-image-modern {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 70%;
  overflow: hidden;
}

.photo-image-modern img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.photo-card-modern:hover .photo-image-modern img {
  transform: scale(1.08);
}

.photo-badge-modern {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: var(--primary-gradient);
  color: white;
  border-radius: 20px;
  padding: 0.25rem 0.75rem;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 2px 6px rgba(69, 104, 220, 0.3);
  z-index: 2;
}

.photo-info-modern {
  padding: 1.2rem;
}

.photo-title-modern {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-meta-modern {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.meta-row-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-icon-modern {
  font-size: 1rem;
  color: var(--primary);
  opacity: 0.8;
}

/* 信息面板 */
.info-panel-modern {
  padding: 1.2rem 1.5rem;
  border-bottom: 1px solid var(--border);
  transition: var(--transition);
}

.info-panel-modern:last-child {
  border-bottom: none;
}

.info-panel-modern:hover {
  background-color: rgba(69, 104, 220, 0.03);
}

.info-label-modern {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.35rem;
  gap: 0.5rem;
}

.info-value-modern {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.info-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(69, 104, 220, 0.1);
  color: var(--primary);
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  margin-left: 0.5rem;
}

/* 按钮样式 */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary-modern {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 4px 12px rgba(69, 104, 220, 0.3);
}

.btn-primary-modern:hover {
  box-shadow: 0 6px 16px rgba(69, 104, 220, 0.4);
  transform: translateY(-2px);
}

.btn-secondary-modern {
  background-color: rgba(69, 104, 220, 0.1);
  color: var(--primary);
}

.btn-secondary-modern:hover {
  background-color: rgba(69, 104, 220, 0.15);
  transform: translateY(-2px);
}

.btn-outline-modern {
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--text-secondary);
}

.btn-outline-modern:hover {
  border-color: var(--primary);
  color: var(--primary);
  background-color: rgba(69, 104, 220, 0.03);
  transform: translateY(-2px);
}

/* 图表区域 */
.chart-section-modern {
  background: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid var(--border);
  margin-bottom: 1.5rem;
}

.chart-header-modern {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  border-bottom: 1px solid var(--border);
  gap: 0.75rem;
}

.chart-icon-modern {
  font-size: 1.25rem;
  color: var(--primary);
}

.chart-title-modern {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.chart-content-modern {
  padding: 1.5rem;
}

/* 照片查看器 */
.photo-viewer-modern {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: none;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.photo-viewer-content-modern {
  max-width: 90%;
  max-height: 90%;
  position: relative;
  animation: fadeZoomIn 0.3s ease;
}

.photo-viewer-close-modern {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 28px;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: var(--transition);
  z-index: 10;
}

.photo-viewer-close-modern:hover {
  opacity: 1;
  transform: rotate(90deg);
}

.photo-viewer-img-modern {
  max-width: 100%;
  max-height: 85vh;
  display: block;
  border-radius: var(--radius-sm);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.photo-viewer-title-modern {
  color: white;
  text-align: center;
  margin-top: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.photo-viewer-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  cursor: pointer;
  border: none;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  transition: var(--transition);
}

.photo-viewer-prev {
  left: -80px;
}

.photo-viewer-next {
  right: -80px;
}

.photo-viewer-nav:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 无数据状态 */
.empty-state-modern {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.empty-icon-modern {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 0.9;
}

.empty-title-modern {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
}

.empty-text-modern {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  max-width: 500px;
  font-size: 1.05rem;
  line-height: 1.6;
}

.empty-actions-modern {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* 动画 */
@keyframes fadeZoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(69, 104, 220, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(69, 104, 220, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(69, 104, 220, 0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .nav-container-modern {
    flex-direction: column;
    padding: 0.75rem;
  }
  
  .logo-modern {
    margin-bottom: 0.75rem;
  }
  
  .nav-links-modern {
    width: 100%;
    justify-content: space-around;
    gap: 0.25rem;
  }
  
  .nav-links-modern li a {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .welcome-title {
    font-size: 1.5rem;
  }
  
  .welcome-subtitle {
    font-size: 1rem;
  }
  
  .stats-grid-modern {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1rem;
  }
  
  .stat-card-modern {
    padding: 1.25rem;
  }
  
  .stat-icon-wrapper {
    width: 40px;
    height: 40px;
  }
  
  .stat-value-modern {
    font-size: 1.6rem;
  }
  
  .photos-grid-modern {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .photo-title-modern {
    font-size: 0.9rem;
  }
  
  .card-header-modern {
    padding: 1rem;
  }
  
  .card-content-modern {
    padding: 1rem;
  }
}

/* 工具类 */
.text-primary {
  color: var(--primary) !important;
}

.text-secondary {
  color: var(--secondary) !important;
}

.bg-primary-light {
  background-color: rgba(69, 104, 220, 0.1) !important;
}

.border-primary {
  border-color: var(--primary) !important;
}

.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 禁用动画 - 可选类 */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

/* 覆盖一些基础样式 */
a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  margin-top: 0;
}
