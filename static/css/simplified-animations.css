/* 简化动画样式覆盖文件 */

/* 全局减少动画时间 */
* {
    transition-duration: 0.15s !important; /* 覆盖所有过渡时间为更短的值 */
}

/* 简化加载动画 */
.ai-loading {
    border-left: 3px solid #4f46e5;
    padding-left: 10px;
}

/* 替换多点加载动画为单一指示器 */
.ai-loading-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #4f46e5;
    border-right-color: transparent;
    border-radius: 50%;
    animation: simple-rotate 1s linear infinite;
}

/* 简化的旋转动画 */
@keyframes simple-rotate {
    to { transform: rotate(360deg); }
}

/* 禁用不必要的动画效果 */
.card, .button, .nav-link, .modal, .dropdown-menu {
    transition: none !important;
}

/* 简化通知动画 */
.notification {
    animation: none !important;
    transition: opacity 0.15s ease !important;
}

/* 禁用所有hover时的缩放和阴影变化 */
*:hover {
    transform: none !important;
    box-shadow: inherit !important;
}

/* 简化按钮样式，移除阴影变化 */
.btn, button {
    transition: background-color 0.15s ease, color 0.15s ease !important;
    transform: none !important;
}

/* 简化图表交互 */
.plotly-chart {
    transition: none !important;
}
