/* 照片分析器美化样式 - 基于现有界面优化 */

/* 全局样式优化 */
:root {
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #f093fb;
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-color: #4ecdc4;
    --warning-color: #fce38a;
    --error-color: #ff6b6b;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --bg-primary: #f8f9fa;
    --bg-card: #ffffff;
    --shadow-light: 0 4px 12px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 16px 48px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 背景渐变优化 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

/* 现代化仪表板容器 */
.modern-dashboard {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    margin: 1rem;
    padding: 0;
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 头部区域美化 */
.dashboard-header {
    background: var(--primary-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.06)"/><circle cx="70" cy="80" r="2.5" fill="rgba(255,255,255,0.04)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.header-content {
    position: relative;
    z-index: 1;
}

.dashboard-title {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-icon {
    font-size: 3rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.dashboard-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin: 0.5rem 0 0 0;
    font-weight: 400;
}

/* 统计卡片美化 */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.02);
}

.stat-card-modern {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stat-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.stat-card-modern:nth-child(2)::before {
    background: var(--secondary-gradient);
}

.stat-card-modern:nth-child(3)::before {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.stat-card-modern:nth-child(4)::before {
    background: linear-gradient(135deg, #fce38a 0%, #f38181 100%);
}

.stat-card-modern:nth-child(5)::before {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-card-modern:nth-child(6)::before {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 主要内容区域美化 */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    padding: 2rem;
    background: var(--bg-primary);
}

/* 最佳照片展示美化 */
.best-photos-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.section-icon {
    font-size: 1.8rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.section-subtitle {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 0.95rem;
}

.best-photos-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.photo-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.photo-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.photo-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.photo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-card:hover .photo-image img {
    transform: scale(1.05);
}

.photo-score {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.photo-details {
    padding: 1.5rem;
}

.photo-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.meta-icon {
    font-size: 1rem;
    width: 1.2rem;
    text-align: center;
}

.view-photo-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.view-photo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* 信息面板美化 */
.content-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-panel {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header {
    background: var(--primary-gradient);
    padding: 1rem 1.5rem;
    color: white;
}

.panel-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-icon {
    font-size: 1.3rem;
}

.panel-content {
    padding: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.info-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.info-count {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

/* 图表区域美化 */
.charts-dashboard {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    margin: 2rem;
    margin-top: 0;
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.charts-header {
    text-align: center;
    margin-bottom: 2rem;
}

.charts-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.charts-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.charts-subtitle {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

.charts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.chart-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin: 0.25rem 0 0 0;
}

.chart-content {
    padding: 1.5rem;
    min-height: 300px;
}

/* 操作按钮美化 */
.dashboard-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.02);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-btn.primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.action-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.action-btn.secondary {
    background: var(--secondary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
}

.action-btn.secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.6);
}

.action-btn.outline {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-light);
}

.action-btn.outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.btn-icon {
    font-size: 1.2rem;
}

/* 空状态美化 */
.empty-dashboard {
    padding: 4rem 2rem;
    text-align: center;
    background: var(--bg-primary);
}

.empty-content {
    max-width: 600px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 3rem;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-icon {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.empty-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
}

.empty-description {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.empty-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary-large, .btn-secondary-large {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary-large {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-secondary-large {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-light);
}

.btn-secondary-large:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.btn-icon {
    font-size: 1.2rem;
}

/* 照片查看器美化 */
.photo-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.photo-viewer-overlay.active {
    opacity: 1;
    visibility: visible;
}

.photo-viewer-content {
    max-width: 90%;
    max-height: 90%;
    position: relative;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-heavy);
    transform: scale(0.8);
    transition: var(--transition);
}

.photo-viewer-overlay.active .photo-viewer-content {
    transform: scale(1);
}

.photo-viewer-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.photo-viewer-close:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.photo-viewer-image {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
}

.photo-viewer-title {
    text-align: center;
    margin-top: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
    }
    
    .title-icon {
        font-size: 2.5rem;
    }
    
    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .best-photos-container {
        grid-template-columns: 1fr;
    }
    
    .chart-section {
        grid-template-columns: 1fr;
    }
    
    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary-large, .btn-secondary-large {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card-modern {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card-modern:nth-child(1) { animation-delay: 0.1s; }
.stat-card-modern:nth-child(2) { animation-delay: 0.2s; }
.stat-card-modern:nth-child(3) { animation-delay: 0.3s; }
.stat-card-modern:nth-child(4) { animation-delay: 0.4s; }
.stat-card-modern:nth-child(5) { animation-delay: 0.5s; }
.stat-card-modern:nth-child(6) { animation-delay: 0.6s; }

.photo-card {
    animation: fadeInUp 0.8s ease-out;
}

.photo-card:nth-child(1) { animation-delay: 0.2s; }
.photo-card:nth-child(2) { animation-delay: 0.4s; }
.photo-card:nth-child(3) { animation-delay: 0.6s; }
