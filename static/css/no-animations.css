/* 
 * 禁用动画覆盖样式 - 完全移除所有动画和过渡效果
 */

/* 禁用全局过渡效果 */
* {
    transition: none !important;
    animation: none !important;
}

/* 禁用加载遮罩的动画和效果 */
.loading-overlay {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* 禁用加载旋转器 */
.spinner {
    animation: none !important;
    border-color: #667eea !important;
}

/* 禁用图表过渡动画 */
.js-plotly-plot .plotly .g-gtitle,
.js-plotly-plot .plotly .g-xtitle,
.js-plotly-plot .plotly .g-ytitle {
    transform: none !important;
    transition: none !important;
}

/* 禁用图表元素动画 */
.js-plotly-plot .plotly .trace,
.js-plotly-plot .plotly .points,
.js-plotly-plot .plotly .bars,
.js-plotly-plot .plotly .scattergl,
.js-plotly-plot .plotly .scatterpolargl {
    transition: none !important;
}

/* 禁用各种UI元素的过渡效果 */
.card, 
.button, 
.nav-link, 
.modal, 
.dropdown-menu,
.notification,
.btn, 
button,
input,
select,
.tab,
.path-item,
.file-item {
    transition: none !important;
    animation: none !important;
}

/* AI分析加载状态简化 */
.ai-loading {
    border-left: 3px solid #4f46e5;
    padding-left: 10px;
    animation: none !important;
}

.ai-loading-indicator,
.ai-loading-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #4f46e5;
    border-radius: 50%;
    animation: none !important;
    margin-left: 5px;
}

/* 页面切换和内容更新无动画 */
.content,
.main-content,
.section,
.tab-content {
    transition: none !important;
    animation: none !important;
    opacity: 1 !important;
}
