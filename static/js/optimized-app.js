// 优化后的照片分析器 JavaScript
// 减少动画，专注于核心功能

// 全局变量
let currentCharts = {};
let isLoading = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 应用初始化
function initializeApp() {
    setActiveNavigation();
    initializeFileUpload();
    
    // 先加载保存的分析结果
    loadSavedAnalysisResults();
    
    // 然后检查LLM状态（如果有新数据会自动分析）
    setTimeout(() => {
        checkLlmStatus();
    }, 1000);
}

// 设置导航高亮
function setActiveNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-links a');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath || 
            (currentPath === '/' && link.getAttribute('href') === '/')) {
            link.classList.add('active');
        }
    });
}

// 加载概览图表
async function loadOverviewCharts(retryCount = 0) {
    try {
        showLoading();
        
        // 添加超时控制
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 10000); // 10秒超时
        
        const response = await fetch('/api/overview-charts', {
            signal: controller.signal
        }).finally(() => {
            clearTimeout(timeout);
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const charts = await response.json();
        renderOverviewCharts(charts);
        hideLoading();
    } catch (error) {
        console.error('加载图表失败:', error);
        
        // 如果是超时或网络错误，尝试重试
        if ((error.name === 'AbortError' || error.message.includes('network')) && retryCount < 2) {
            console.log(`尝试重新加载图表，第 ${retryCount + 1} 次重试`);
            showError('加载图表超时，正在重试...');
            setTimeout(() => loadOverviewCharts(retryCount + 1), 1000);
            return;
        }
        
        showError('加载图表失败: ' + error.message);
        hideLoading();
    }
}

// 渲染概览图表
function renderOverviewCharts(charts) {
    if (typeof Plotly === 'undefined') {
        showError('图表库未加载，请检查网络连接');
        return;
    }
    
    // 渲染设备镜头统计信息
    if (charts.device_lens_info) {
        const info = charts.device_lens_info;
        
        // 批量更新DOM以减少重排
        const updates = {
            'top-device': info.top_device || '暂无数据',
            'top-device-count': info.top_device_count ? `${info.top_device_count}` : '',
            'top-lens': info.top_lens || '暂无数据',
            'top-lens-count': info.top_lens_count ? `${info.top_lens_count}` : '',
            'device-lens-combo': info.device_lens_combo || '暂无数据',
            'device-lens-combo-count': info.device_lens_combo_count ? `${info.device_lens_combo_count}` : ''
        };
        
        // 一次性更新所有元素
        Object.keys(updates).forEach(id => {
            updateElement(id, updates[id]);
        });
    }
    
    // 直接渲染图表，不使用动画
    // 渲染月份分布图
    if (charts.months) {
        try {
            const monthsChart = JSON.parse(charts.months);
            // 简化Plotly配置，提高性能
            if (monthsChart.layout) {
                // 简化布局配置
                delete monthsChart.layout.annotations;
                delete monthsChart.layout.shapes;
                
                // 禁用过渡动画
                monthsChart.layout.transition = {
                    duration: 0
                };
            }
            
            Plotly.newPlot('months-chart', monthsChart.data, monthsChart.layout, {
                responsive: true,
                displayModeBar: false,
                staticPlot: true // 静态图表模式，大幅提高性能
            });
            currentCharts['months'] = monthsChart;
        } catch (error) {
            console.error('月份图表渲染失败:', error);
        }
    }
    
    // 直接渲染第二个图表，不使用setTimeout
    // 渲染时段分布图
    if (charts.time_periods) {
        try {
            const timePeriodsChart = JSON.parse(charts.time_periods);
            // 简化Plotly配置，提高性能
            if (timePeriodsChart.layout) {
                // 简化布局配置
                delete timePeriodsChart.layout.annotations;
                delete timePeriodsChart.layout.shapes;
                
                // 禁用过渡动画
                timePeriodsChart.layout.transition = {
                    duration: 0
                };
            }
            
            Plotly.newPlot('time-periods-chart', timePeriodsChart.data, timePeriodsChart.layout, {
                responsive: true,
                displayModeBar: false,
                staticPlot: true // 静态图表模式，大幅提高性能
            });
            currentCharts['time_periods'] = timePeriodsChart;
        } catch (error) {
            console.error('时段图表渲染失败:', error);
        }
    }
}

// 加载数据图表
async function loadDataCharts() {
    try {
        showLoading();
        const response = await fetch('/api/data-charts');
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const charts = await response.json();
        renderCharts(charts);
        hideLoading();
    } catch (error) {
        console.error('加载数据图表失败:', error);
        showError('加载数据图表失败: ' + error.message);
        hideLoading();
    }
}

// 渲染数据图表
function renderCharts(charts) {
    const chartTypes = ['focal_lengths', 'apertures', 'shutter_speeds', 'iso_values'];
    const chartConfig = {
        responsive: true,
        displayModeBar: false
    };
    
    let hasData = false;
    
    chartTypes.forEach(chartType => {
        if (charts[chartType]) {
            try {
                const chartData = JSON.parse(charts[chartType]);
                Plotly.newPlot(`${chartType.replace('_', '-')}-chart`, chartData.data, chartData.layout, chartConfig);
                // 存储图表数据供AI分析使用
                currentCharts[chartType] = chartData;
                hasData = true;
            } catch (error) {
                console.error(`${chartType}图表渲染失败:`, error);
            }
        } else {
            const chartElement = document.getElementById(`${chartType.replace('_', '-')}-chart`);
            if (chartElement) {
                chartElement.innerHTML = '<p style="text-align: center; color: #64748b; padding: 2rem;">暂无数据</p>';
            }
        }
    });
    
    // 如果有数据，检查LLM状态并自动分析
    if (hasData) {
        setTimeout(() => {
            checkLlmStatus();
        }, 500);
    }
}

// 加载设备图表
async function loadDeviceCharts() {
    try {
        showLoading();
        const response = await fetch('/api/device-charts');
        
        if (!response.ok) {
            throw new Error('无法获取设备图表');
        }
        
        const charts = await response.json();
        renderDeviceCharts(charts);
        hideLoading();
    } catch (error) {
        console.error('加载设备图表失败:', error);
        showError('加载设备图表失败: ' + error.message);
        hideLoading();
    }
}

// 渲染设备图表
function renderDeviceCharts(charts) {
    const chartConfig = {
        responsive: true,
        displayModeBar: false
    };
    
    let hasData = false;
    
    if (charts.devices) {
        try {
            const devicesChart = JSON.parse(charts.devices);
            Plotly.newPlot('devices-chart', devicesChart.data, devicesChart.layout, chartConfig);
            // 存储图表数据供AI分析使用
            currentCharts['devices'] = devicesChart;
            hasData = true;
        } catch (error) {
            console.error('设备图表渲染失败:', error);
        }
    }
    
    if (charts.lenses) {
        try {
            const lensesChart = JSON.parse(charts.lenses);
            Plotly.newPlot('lenses-chart', lensesChart.data, lensesChart.layout, chartConfig);
            // 存储图表数据供AI分析使用
            currentCharts['lenses'] = lensesChart;
            hasData = true;
        } catch (error) {
            console.error('镜头图表渲染失败:', error);
        }
    }
    
    // 如果有数据，检查LLM状态并自动分析
    if (hasData) {
        setTimeout(() => {
            checkLlmStatus();
        }, 500);
    }
}

// 添加路径
async function addPath() {
    const pathInput = document.getElementById('new-path');
    const path = pathInput.value.trim();
    
    if (!path) {
        showError('请输入路径');
        return;
    }
    
    if (path.length < 3) {
        showError('请输入有效的文件夹路径');
        return;
    }
    
    try {
        showLoading('正在添加路径...');
        
        const formData = new FormData();
        formData.append('path', path);
        
        const response = await fetch('/api/add-path', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showSuccess(result.message);
            pathInput.value = '';
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('添加路径失败:', error);
        hideLoading();
        showError('添加路径失败: ' + error.message);
    }
}

// 删除路径
async function removePath(path) {
    if (!confirm('确定要删除这个路径吗？')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('path', path);
        
        const response = await fetch('/api/remove-path', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('删除路径失败:', error);
        showError('删除路径失败: ' + error.message);
    }
}

// 重新分析照片
async function analyzePhotos() {
    try {
        showLoading('正在分析照片，请稍候...');
        
        const response = await fetch('/api/analyze', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            
            // 重新加载图表而不是刷新页面
            const currentPath = window.location.pathname;
            
            // 延迟一点让服务器处理完数据
            setTimeout(async () => {
                if (currentPath === '/data') {
                    // 重新加载数据图表
                    await loadDataCharts();
                } else if (currentPath === '/devices') {
                    // 重新加载设备图表
                    await loadDeviceCharts();
                } else {
                    // 其他页面刷新
                    location.reload();
                }
            }, 1500);
        } else {
            showError(result.message);
        }
        
        hideLoading();
    } catch (error) {
        console.error('分析失败:', error);
        showError('分析失败: ' + error.message);
        hideLoading();
    }
}

// 选择文件夹
function selectFolder() {
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true;
    input.directory = true;
    input.multiple = true;
    
    input.onchange = function(event) {
        const files = event.target.files;
        if (files.length > 0) {
            const file = files[0];
            let folderName = '';
            
            if (file.webkitRelativePath) {
                folderName = file.webkitRelativePath.split('/')[0];
            }
            
            showFolderSelectionResult(folderName, files.length);
        }
    };
    
    input.click();
}

// 显示文件夹选择结果
function showFolderSelectionResult(folderName, fileCount) {
    const pathInput = document.getElementById('new-path');
    if (pathInput) {
        pathInput.placeholder = `请输入包含"${folderName}"的完整路径`;
        pathInput.focus();
    }
    
    showNotification(`已选择文件夹"${folderName}" (${fileCount}个文件)，请输入完整路径`, 'info');
}

// 文件上传初始化
function initializeFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    if (uploadArea && fileInput) {
        // 拖拽上传
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files);
            }
        });
        
        // 点击上传
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFileUpload(files);
            }
        });
    }
}

// 处理文件上传
function handleFileUpload(files) {
    const formData = new FormData();
    
    // 过滤图片文件
    const imageFiles = Array.from(files).filter(file => 
        file.type.startsWith('image/')
    );
    
    if (imageFiles.length === 0) {
        showError('请选择图片文件');
        return;
    }
    
    imageFiles.forEach(file => {
        formData.append('files', file);
    });
    
    uploadFiles(formData);
}

// 上传文件
async function uploadFiles(formData) {
    try {
        showLoading('正在上传文件...');
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showSuccess(`成功上传 ${result.count} 个文件`);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('上传失败:', error);
        hideLoading();
        showError('上传失败: ' + error.message);
    }
}

// 清空所有路径
async function clearAllPaths() {
    if (!confirm('确定要清空所有路径吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        showLoading('正在清空路径...');
        
        const response = await fetch('/api/clear-paths', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('清空路径失败:', error);
        hideLoading();
        showError('清空路径失败: ' + error.message);
    }
}

// 清除缓存
async function clearCache() {
    if (!confirm('确定要清除缓存吗？这将删除所有分析数据，但保留路径配置。')) {
        return;
    }
    
    try {
        showLoading('正在清除缓存...');
        
        const response = await fetch('/api/clear-cache', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('清除缓存失败:', error);
        hideLoading();
        showError('清除缓存失败: ' + error.message);
    }
}

// 重置设置
async function resetSettings() {
    if (!confirm('确定要重置所有设置吗？此操作将清空所有路径和数据，且不可撤销！')) {
        return;
    }
    
    try {
        showLoading('正在重置设置...');
        
        const response = await fetch('/api/reset-settings', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('重置设置失败:', error);
        hideLoading();
        showError('重置设置失败: ' + error.message);
    }
}

// 刷新系统
function refreshSystem() {
    showLoading('系统刷新中...');
    setTimeout(() => {
        location.reload();
        hideLoading();
    }, 1000);
}

// 导出设置
async function exportSettings() {
    try {
        showLoading('正在准备导出...');
        
        const response = await fetch('/api/export-settings', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            // 创建下载链接
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(result.config, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "photo_analyzer_config.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            showSuccess('设置已导出');
        } else {
            showError(result.message || '导出失败');
        }
    } catch (error) {
        console.error('导出设置失败:', error);
        hideLoading();
        showError('导出设置失败: ' + error.message);
    }
}

// 导入设置
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    
    input.onchange = async function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = async function(e) {
            try {
                const config = JSON.parse(e.target.result);
                
                showLoading('正在导入设置...');
                
                const response = await fetch('/api/import-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                hideLoading();
                
                if (result.success) {
                    showSuccess(result.message);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError(result.message);
                }
            } catch (error) {
                console.error('导入设置失败:', error);
                hideLoading();
                showError('导入设置失败: 无效的配置文件格式');
            }
        };
        
        reader.readAsText(file);
    };
    
    input.click();
}

// 显示设置页面
function showSettingsPage(pageId) {
    document.getElementById('main-settings').style.display = 'none';
    
    // 隐藏所有设置页面
    document.querySelectorAll('.settings-page').forEach(page => {
        page.style.display = 'none';
    });
    
    // 显示选定的页面
    document.getElementById(pageId).style.display = 'block';
}

// 显示主设置菜单
function showMainSettings() {
    // 隐藏所有设置页面
    document.querySelectorAll('.settings-page').forEach(page => {
        page.style.display = 'none';
    });
    
    // 显示主设置菜单
    document.getElementById('main-settings').style.display = 'block';
}

// 工具函数
function updateElement(id, content) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = content;
    }
}

// 简化版的加载函数 - 不显示任何加载动画
function showLoading(message = '加载中...') {
    // 记录加载状态但不显示任何动画
    isLoading = true;
    console.log(`加载操作开始: ${message}`);
    // 不创建任何加载动画
}

function hideLoading() {
    // 重置加载状态但不进行任何DOM操作
    isLoading = false;
    console.log('加载操作结束');
}

function showError(message) {
    showNotification(message, 'danger');
}

function showSuccess(message) {
    showNotification(message, 'success');
}

function showNotification(message, type = 'info') {
    // 移除之前的通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `alert alert-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;
    notification.innerHTML = message;
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// 检查LLM状态
async function checkLlmStatus() {
    // 调用异步分析模块的检查函数
    checkLlmStatusAndAnalyze();
}

// 分析图表数据 - 转发到异步分析函数
async function analyzeChart(chartType, forceNew = false) {
    // 调用异步分析函数
    asyncAnalyzeChart(chartType, forceNew);
}

// 加载保存的分析结果
// 加载保存的分析结果 - 转发到异步分析模块
async function loadSavedAnalysisResults() {
    // 调用异步分析模块的加载函数
    if (typeof window.loadSavedAnalysisResults === 'function') {
        window.loadSavedAnalysisResults();
    }
}

// 更新图表数据到全局变量
function updateChartData(type, data) {
    currentCharts[type] = data;
}

// 窗口大小变化时重新渲染图表
window.addEventListener('resize', function() {
    setTimeout(() => {
        Object.keys(currentCharts).forEach(chartKey => {
            const chartElement = document.getElementById(chartKey.replace('_', '-') + '-chart');
            if (chartElement && typeof Plotly !== 'undefined') {
                Plotly.Plots.resize(chartElement);
            }
        });
    }, 100);
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(248, 250, 252, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .loading-text {
        font-size: 1rem;
        color: #64748b;
        font-weight: 500;
    }
`;
document.head.appendChild(style);

// 清除所有分析结果 - 转发到异步分析模块
async function clearAnalysisResults() {
    // 调用异步分析模块的清除函数
    clearAllAnalysisResults();
}

// 清除分析结果并重新分析
async function clearAndReanalyze() {
    try {
        if (!confirm('确定要清除现有分析并重新分析所有数据吗？')) {
            return;
        }
        
        showLoading('正在清除分析结果...');
        
        // 先清除分析结果
        await fetch('/api/clear-analysis', { method: 'POST' });
        
        // 对当前页面的图表数据进行重新分析
        hideLoading();
        showSuccess('已清除旧分析，正在重新分析数据...');
        
        // 获取当前页面路径
        const currentPath = window.location.pathname;
        
        // 根据当前页面路径选择重新加载的图表
        setTimeout(async () => {
            if (currentPath === '/data') {
                await loadDataCharts();
            } else if (currentPath === '/devices') {
                await loadDeviceCharts();
            } else {
                // 在其他页面，刷新页面
                location.reload();
            }
            
            showSuccess('数据重新分析完成');
        }, 500);
    } catch (error) {
        hideLoading();
        showError('重新分析失败: ' + error.message);
    }
}
