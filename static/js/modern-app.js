/**
 * Modern Photo Analyzer JavaScript
 * ES6+ features, improved error handling, and better performance
 */

class PhotoAnalyzer {
  constructor() {
    this.charts = new Map();
    this.isLoading = false;
    this.loadingIndicator = null;
    this.toastContainer = null;
    this.navToggle = null;
    this.navMenu = null;
    
    this.init();
  }

  /**
   * Initialize the application
   */
  init() {
    this.setupDOM();
    this.setupEventListeners();
    this.setupNavigation();
    this.loadSavedAnalysis();
    
    // Check if we're on a page that needs charts
    if (this.shouldLoadCharts()) {
      this.loadCharts();
    }
  }

  /**
   * Setup DOM references
   */
  setupDOM() {
    this.loadingIndicator = document.getElementById('loading-indicator');
    this.toastContainer = document.getElementById('toast-container');
    this.navToggle = document.querySelector('.nav-toggle');
    this.navMenu = document.querySelector('.nav-menu');
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Mobile navigation toggle
    if (this.navToggle && this.navMenu) {
      this.navToggle.addEventListener('click', () => this.toggleMobileNav());
    }

    // Close mobile nav when clicking outside
    document.addEventListener('click', (e) => {
      if (this.navMenu && this.navMenu.classList.contains('open') && 
          !this.navMenu.contains(e.target) && !this.navToggle.contains(e.target)) {
        this.closeMobileNav();
      }
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));

    // Form submissions
    document.addEventListener('submit', (e) => this.handleFormSubmit(e));

    // Window resize
    window.addEventListener('resize', this.debounce(() => this.handleResize(), 250));
  }

  /**
   * Setup navigation highlighting
   */
  setupNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === currentPath) {
        link.classList.add('active');
        link.setAttribute('aria-current', 'page');
      } else {
        link.removeAttribute('aria-current');
      }
    });
  }

  /**
   * Toggle mobile navigation
   */
  toggleMobileNav() {
    if (!this.navMenu || !this.navToggle) return;
    
    const isOpen = this.navMenu.classList.contains('open');
    
    if (isOpen) {
      this.closeMobileNav();
    } else {
      this.openMobileNav();
    }
  }

  /**
   * Open mobile navigation
   */
  openMobileNav() {
    this.navMenu.classList.add('open');
    this.navToggle.setAttribute('aria-expanded', 'true');
    document.body.style.overflow = 'hidden';
  }

  /**
   * Close mobile navigation
   */
  closeMobileNav() {
    this.navMenu.classList.remove('open');
    this.navToggle.setAttribute('aria-expanded', 'false');
    document.body.style.overflow = '';
  }

  /**
   * Handle keyboard events
   */
  handleKeyboard(e) {
    // Close modals with Escape key
    if (e.key === 'Escape') {
      this.closeAllModals();
      this.closeMobileNav();
    }
  }

  /**
   * Handle form submissions
   */
  async handleFormSubmit(e) {
    const form = e.target;
    if (!form.matches('form[data-async]')) return;
    
    e.preventDefault();
    
    try {
      this.showLoading();
      const formData = new FormData(form);
      const response = await fetch(form.action, {
        method: form.method || 'POST',
        body: formData
      });
      
      const result = await response.json();
      
      if (result.success) {
        this.showToast(result.message || '操作成功', 'success');
        
        // Trigger custom event for form success
        form.dispatchEvent(new CustomEvent('formSuccess', { detail: result }));
      } else {
        this.showToast(result.message || '操作失败', 'error');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      this.showToast('网络错误，请重试', 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Close mobile nav on resize to desktop
    if (window.innerWidth > 768) {
      this.closeMobileNav();
    }
    
    // Trigger chart resize if needed
    this.charts.forEach(chart => {
      if (chart && typeof chart.relayout === 'function') {
        chart.relayout();
      }
    });
  }

  /**
   * Check if current page should load charts
   */
  shouldLoadCharts() {
    const path = window.location.pathname;
    return ['/', '/data', '/devices'].includes(path);
  }

  /**
   * Load charts based on current page
   */
  async loadCharts() {
    const path = window.location.pathname;
    
    try {
      switch (path) {
        case '/':
          await this.loadOverviewCharts();
          break;
        case '/data':
          await this.loadDataCharts();
          break;
        case '/devices':
          await this.loadDeviceCharts();
          break;
      }
    } catch (error) {
      console.error('Error loading charts:', error);
      this.showToast('图表加载失败', 'error');
    }
  }

  /**
   * Load overview charts for home page
   */
  async loadOverviewCharts() {
    try {
      const response = await this.fetchWithTimeout('/api/overview-charts', 10000);
      const data = await response.json();
      
      if (data.months) {
        this.renderChart('months-chart', data.months);
      }
      
      if (data.time_periods) {
        this.renderChart('time-periods-chart', data.time_periods);
      }
      
      if (data.device_lens_info) {
        this.updateDeviceLensInfo(data.device_lens_info);
      }
    } catch (error) {
      console.error('Error loading overview charts:', error);
      this.showChartError(['months-chart', 'time-periods-chart']);
    }
  }

  /**
   * Load data charts
   */
  async loadDataCharts() {
    try {
      const response = await this.fetchWithTimeout('/api/data-charts', 10000);
      const data = await response.json();
      
      Object.entries(data).forEach(([key, chartData]) => {
        const elementId = `${key.replace('_', '-')}-chart`;
        this.renderChart(elementId, chartData);
      });
    } catch (error) {
      console.error('Error loading data charts:', error);
      this.showToast('数据图表加载失败', 'error');
    }
  }

  /**
   * Load device charts
   */
  async loadDeviceCharts() {
    try {
      const response = await this.fetchWithTimeout('/api/device-charts', 10000);
      const data = await response.json();
      
      Object.entries(data).forEach(([key, chartData]) => {
        const elementId = `${key}-chart`;
        this.renderChart(elementId, chartData);
      });
    } catch (error) {
      console.error('Error loading device charts:', error);
      this.showToast('设备图表加载失败', 'error');
    }
  }

  /**
   * Render a chart using Plotly
   */
  renderChart(elementId, chartData) {
    const element = document.getElementById(elementId);
    if (!element) {
      console.warn(`Chart element ${elementId} not found`);
      return;
    }

    try {
      // Show loading state
      element.innerHTML = '<div class="chart-loading"><div class="loading-spinner"></div><span>加载图表中...</span></div>';
      
      // Configure Plotly options
      const config = {
        responsive: true,
        displayModeBar: false,
        locale: 'zh-CN'
      };
      
      const layout = {
        ...chartData.layout,
        font: { family: 'Inter, sans-serif' },
        paper_bgcolor: 'transparent',
        plot_bgcolor: 'transparent',
        margin: { t: 40, r: 40, b: 40, l: 40 }
      };
      
      // Create the chart
      Plotly.newPlot(element, chartData.data, layout, config)
        .then(() => {
          this.charts.set(elementId, element);
        })
        .catch(error => {
          console.error(`Error rendering chart ${elementId}:`, error);
          this.showChartError([elementId]);
        });
    } catch (error) {
      console.error(`Error setting up chart ${elementId}:`, error);
      this.showChartError([elementId]);
    }
  }

  /**
   * Show chart error state
   */
  showChartError(elementIds) {
    elementIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.innerHTML = `
          <div class="chart-error">
            <span style="font-size: 2rem;">📊</span>
            <span>图表加载失败</span>
            <button class="btn btn-sm btn-outline" onclick="location.reload()">重新加载</button>
          </div>
        `;
      }
    });
  }

  /**
   * Update device and lens info
   */
  updateDeviceLensInfo(info) {
    const updates = [
      { id: 'top-device', value: info.top_device?.name || '未知' },
      { id: 'top-device-count', value: info.top_device?.count ? `(${info.top_device.count}张)` : '' },
      { id: 'top-lens', value: info.top_lens?.name || '未知' },
      { id: 'top-lens-count', value: info.top_lens?.count ? `(${info.top_lens.count}张)` : '' },
      { id: 'device-lens-combo', value: info.top_combo?.name || '未知' },
      { id: 'device-lens-combo-count', value: info.top_combo?.count ? `(${info.top_combo.count}张)` : '' },
      { id: 'active-months', value: info.active_months?.join(', ') || '未知' }
    ];

    updates.forEach(({ id, value }) => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = value;
      }
    });
  }

  /**
   * Load saved analysis results
   */
  async loadSavedAnalysis() {
    try {
      const response = await fetch('/api/analysis-results');
      const results = await response.json();

      // Process and display saved analysis
      Object.entries(results).forEach(([chartType, result]) => {
        if (result.structured) {
          this.displayAnalysisResult(chartType, result);
        }
      });
    } catch (error) {
      console.error('Error loading saved analysis:', error);
    }
  }

  /**
   * Display analysis result
   */
  displayAnalysisResult(chartType, result) {
    const container = document.getElementById(`${chartType}-analysis`);
    if (!container) return;

    const structured = result.structured;
    const html = `
      <div class="analysis-result">
        <h4 class="analysis-title">${structured.title || '分析结果'}</h4>
        <div class="analysis-summary">${structured.summary || ''}</div>
        ${structured.insights ? `
          <div class="analysis-insights">
            <h5>关键洞察</h5>
            <ul>
              ${structured.insights.map(insight => `<li>${insight}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
        ${structured.recommendations ? `
          <div class="analysis-recommendations">
            <h5>建议</h5>
            <ul>
              ${structured.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;

    container.innerHTML = html;
  }

  /**
   * Show loading indicator
   */
  showLoading(message = '加载中...') {
    if (this.loadingIndicator) {
      const textElement = this.loadingIndicator.querySelector('.loading-text');
      if (textElement) {
        textElement.textContent = message;
      }
      this.loadingIndicator.classList.add('show');
      this.loadingIndicator.setAttribute('aria-hidden', 'false');
    }
    this.isLoading = true;
  }

  /**
   * Hide loading indicator
   */
  hideLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.remove('show');
      this.loadingIndicator.setAttribute('aria-hidden', 'true');
    }
    this.isLoading = false;
  }

  /**
   * Show toast notification
   */
  showToast(message, type = 'info', duration = 5000) {
    if (!this.toastContainer) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
      <div class="toast-content">
        <span class="toast-message">${message}</span>
        <button class="toast-close" aria-label="关闭通知">&times;</button>
      </div>
    `;

    // Add close functionality
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', () => this.removeToast(toast));

    // Auto remove after duration
    setTimeout(() => this.removeToast(toast), duration);

    this.toastContainer.appendChild(toast);
  }

  /**
   * Remove toast notification
   */
  removeToast(toast) {
    if (toast && toast.parentNode) {
      toast.style.animation = 'slideOut 0.3s ease-in forwards';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }
  }

  /**
   * Close all modals
   */
  closeAllModals() {
    const modals = document.querySelectorAll('.modal-overlay.active');
    modals.forEach(modal => {
      modal.classList.remove('active');
      document.body.style.overflow = '';
    });
  }

  /**
   * Fetch with timeout
   */
  async fetchWithTimeout(url, timeout = 8000) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      throw error;
    }
  }

  /**
   * Debounce function
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// Global functions for backward compatibility
window.analyzePhotos = async function() {
  const app = window.photoAnalyzer;
  if (!app) return;

  try {
    app.showLoading('分析照片中...');
    const response = await fetch('/api/analyze', { method: 'POST' });
    const result = await response.json();

    if (result.success) {
      app.showToast(result.message, 'success');
      setTimeout(() => location.reload(), 2000);
    } else {
      app.showToast(result.message || '分析失败', 'error');
    }
  } catch (error) {
    console.error('Analysis error:', error);
    app.showToast('分析失败，请重试', 'error');
  } finally {
    app.hideLoading();
  }
};

window.viewFullPhoto = function(photoPath, photoName) {
  const overlay = document.createElement('div');
  overlay.className = 'modal-overlay photo-viewer';
  overlay.innerHTML = `
    <div class="modal-content photo-viewer">
      <button class="modal-close" onclick="this.closest('.modal-overlay').remove(); document.body.style.overflow = ''">&times;</button>
      <img src="/api/photo/${encodeURIComponent(photoPath)}" alt="${photoName}" style="max-width: 100%; max-height: 80vh; object-fit: contain;">
      <div class="photo-viewer-title">${photoName}</div>
    </div>
  `;

  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      overlay.remove();
      document.body.style.overflow = '';
    }
  });

  document.body.appendChild(overlay);
  document.body.style.overflow = 'hidden';

  // Trigger animation
  requestAnimationFrame(() => {
    overlay.classList.add('active');
  });
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.photoAnalyzer = new PhotoAnalyzer();
});

// Add slideOut animation for toasts
const style = document.createElement('style');
style.textContent = `
  @keyframes slideOut {
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);
