// 全局变量
let currentCharts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化应用...');
    initializeApp();
});

// 应用初始化
function initializeApp() {
    console.log('正在初始化应用...');
    // 设置导航高亮
    setActiveNavigation();
    
    // 初始化文件上传功能
    initializeFileUpload();
    
    // 初始化LLM状态
    checkLlmStatus();
    
    // 注意：图表加载现在由模板中的脚本调用，避免重复加载
    console.log('应用初始化完成');
}

// 设置导航高亮
function setActiveNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-links a');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath || 
            (currentPath === '/' && link.getAttribute('href') === '/')) {
            link.classList.add('active');
        }
    });
}

// 加载概览图表
async function loadOverviewCharts() {
    console.log('开始加载概览图表...');
    try {
        showLoading();
        console.log('正在请求 /api/overview-charts...');
        const response = await fetch('/api/overview-charts');
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        console.log('API响应成功，解析JSON数据...');
        const charts = await response.json();
        console.log('获取到的图表数据:', Object.keys(charts));
        
        renderOverviewCharts(charts);
        hideLoading();
        console.log('概览图表加载完成');
    } catch (error) {
        console.error('加载图表失败:', error);
        showError('加载图表失败: ' + error.message);
        hideLoading();
    }
}

// 渲染概览图表
function renderOverviewCharts(charts) {
    console.log('开始渲染概览图表...');
    
    // 检查Plotly是否可用
    if (typeof Plotly === 'undefined') {
        console.error('Plotly未加载!');
        showError('图表库未加载，请检查网络连接');
        return;
    }
    
    // 渲染设备镜头统计信息
    if (charts.device_lens_info) {
        console.log('渲染设备镜头统计信息...');
        const info = charts.device_lens_info;
        
        // 设备统计
        const totalDevicesElement = document.getElementById('total-devices');
        if (totalDevicesElement) {
            totalDevicesElement.textContent = info.total_devices || '0';
        }
        
        document.getElementById('top-device').textContent = info.top_device || '暂无数据';
        document.getElementById('top-device-count').textContent = info.top_device_count ? `(${info.top_device_count} 张)` : '';
        
        // 镜头统计
        const totalLensesElement = document.getElementById('total-lenses');
        if (totalLensesElement) {
            totalLensesElement.textContent = info.total_lenses || '0';
        }
        
        document.getElementById('top-lens').textContent = info.top_lens || '暂无数据';
        document.getElementById('top-lens-count').textContent = info.top_lens_count ? `(${info.top_lens_count} 张)` : '';
        
        // 拍摄偏好
        document.getElementById('device-lens-combo').textContent = info.device_lens_combo || '暂无数据';
        
        // 显示搭配照片数量
        const comboCountElement = document.getElementById('device-lens-combo-count');
        if (comboCountElement) {
            comboCountElement.textContent = info.device_lens_combo_count ? `(${info.device_lens_combo_count} 张)` : '';
        }
        
        // 活跃月份
        const activeMonthsElement = document.getElementById('active-months');
        if (activeMonthsElement && info.active_months) {
            activeMonthsElement.textContent = info.active_months;
        }
        
        console.log('设备镜头统计信息渲染完成');
    } else {
        console.log('没有设备镜头统计数据');
        // 设置默认值
        const defaultElements = [
            'total-devices', 'top-device', 'total-lenses', 'top-lens', 
            'device-lens-combo', 'active-months'
        ];
        defaultElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = '暂无数据';
            }
        });
    }
    
    // 渲染月份分布图
    if (charts.months) {
        console.log('渲染月份分布曲线图...');
        try {
            const monthsChart = JSON.parse(charts.months);
            console.log('月份图表数据解析成功');
            Plotly.newPlot('months-chart', monthsChart.data, monthsChart.layout, {responsive: true});
            currentCharts['months'] = monthsChart;
            console.log('月份曲线图渲染完成');
        } catch (error) {
            console.error('月份图表渲染失败:', error);
        }
    } else {
        console.log('没有月份图表数据');
    }
    
    // 渲染时段分布图
    if (charts.time_periods) {
        console.log('渲染时段分布曲线图...');
        try {
            const timePeriodsChart = JSON.parse(charts.time_periods);
            Plotly.newPlot('time-periods-chart', timePeriodsChart.data, timePeriodsChart.layout, {responsive: true});
            currentCharts['time_periods'] = timePeriodsChart;
            console.log('时段曲线图渲染完成');
        } catch (error) {
            console.error('时段图表渲染失败:', error);
        }
    } else {
        console.log('没有时段图表数据');
    }
    
    console.log('所有图表渲染完成');
}

// 加载数据图表
async function loadDataCharts() {
    console.log('开始加载数据图表...');
    try {
        showLoading();
        console.log('正在请求 /api/data-charts...');
        const response = await fetch('/api/data-charts');
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API响应失败:', response.status, response.statusText);
            console.error('错误详情:', errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }
        
        console.log('API响应成功，解析JSON数据...');
        const charts = await response.json();
        console.log('获取到的数据图表:', Object.keys(charts));
        
        renderDataCharts(charts);
        hideLoading();
        console.log('数据图表加载完成');
    } catch (error) {
        console.error('加载数据图表失败:', error);
        showError('加载数据图表失败: ' + error.message);
        hideLoading();
    }
}

// 渲染数据图表
function renderDataCharts(charts) {
    console.log('开始渲染数据图表...');
    
    // 检查Plotly是否可用
    if (typeof Plotly === 'undefined') {
        console.error('Plotly未加载!');
        showError('图表库未加载，请检查网络连接');
        return;
    }
    
    // 渲染焦距统计
    if (charts.focal_lengths) {
        console.log('渲染焦距统计图...');
        try {
            const focalChart = JSON.parse(charts.focal_lengths);
            Plotly.newPlot('focal-lengths-chart', focalChart.data, focalChart.layout, {responsive: true});
            currentCharts['focal_lengths'] = focalChart;
            console.log('焦距图表渲染完成');
        } catch (error) {
            console.error('焦距图表渲染失败:', error);
        }
    } else {
        console.log('没有焦距图表数据');
        document.getElementById('focal-lengths-chart').innerHTML = '<p style="text-align: center; color: #666;">暂无焦距数据</p>';
    }
    
    // 渲染光圈统计
    if (charts.apertures) {
        console.log('渲染光圈统计图...');
        try {
            const apertureChart = JSON.parse(charts.apertures);
            Plotly.newPlot('apertures-chart', apertureChart.data, apertureChart.layout, {responsive: true});
            currentCharts['apertures'] = apertureChart;
            console.log('光圈图表渲染完成');
        } catch (error) {
            console.error('光圈图表渲染失败:', error);
        }
    } else {
        console.log('没有光圈图表数据');
        document.getElementById('apertures-chart').innerHTML = '<p style="text-align: center; color: #666;">暂无光圈数据</p>';
    }
    
    // 渲染快门速度统计
    if (charts.shutter_speeds) {
        console.log('渲染快门速度统计图...');
        try {
            const shutterChart = JSON.parse(charts.shutter_speeds);
            Plotly.newPlot('shutter-speeds-chart', shutterChart.data, shutterChart.layout, {responsive: true});
            currentCharts['shutter_speeds'] = shutterChart;
            console.log('快门速度图表渲染完成');
        } catch (error) {
            console.error('快门速度图表渲染失败:', error);
        }
    } else {
        console.log('没有快门速度图表数据');
        document.getElementById('shutter-speeds-chart').innerHTML = '<p style="text-align: center; color: #666;">暂无快门速度数据</p>';
    }
    
    // 渲染ISO统计
    if (charts.iso_values) {
        console.log('渲染ISO统计图...');
        try {
            const isoChart = JSON.parse(charts.iso_values);
            Plotly.newPlot('iso-values-chart', isoChart.data, isoChart.layout, {responsive: true});
            currentCharts['iso_values'] = isoChart;
            console.log('ISO图表渲染完成');
        } catch (error) {
            console.error('ISO图表渲染失败:', error);
        }
    } else {
        console.log('没有ISO图表数据');
        document.getElementById('iso-values-chart').innerHTML = '<p style="text-align: center; color: #666;">暂无ISO数据</p>';
    }
    
    console.log('数据图表渲染完成');
}

// 加载设备图表
async function loadDeviceCharts() {
    try {
        showLoading();
        const response = await fetch('/api/device-charts');
        
        if (!response.ok) {
            throw new Error('无法获取设备图表');
        }
        
        const charts = await response.json();
        renderDeviceCharts(charts);
        hideLoading();
    } catch (error) {
        console.error('加载设备图表失败:', error);
        showError('加载设备图表失败: ' + error.message);
        hideLoading();
    }
}

// 渲染设备图表
function renderDeviceCharts(charts) {
    // 渲染设备统计
    if (charts.devices) {
        const devicesChart = JSON.parse(charts.devices);
        Plotly.newPlot('devices-chart', devicesChart.data, devicesChart.layout, {responsive: true});
        currentCharts['devices'] = devicesChart;
    }
    
    // 渲染镜头统计
    if (charts.lenses) {
        const lensesChart = JSON.parse(charts.lenses);
        Plotly.newPlot('lenses-chart', lensesChart.data, lensesChart.layout, {responsive: true});
        currentCharts['lenses'] = lensesChart;
    }
}

// 添加路径
async function addPath() {
    const pathInput = document.getElementById('new-path');
    const path = pathInput.value.trim();
    
    if (!path) {
        showError('请输入路径');
        return;
    }
    
    // 基本路径验证
    if (path.length < 3) {
        showError('请输入有效的文件夹路径');
        return;
    }
    
    try {
        showLoading('正在添加路径...');
        
        const formData = new FormData();
        formData.append('path', path);
        
        const response = await fetch('/api/add-path', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        hideLoading();
        
        if (result.success) {
            showSuccess(result.message);
            pathInput.value = '';
            // 清除路径选择信息
            const pathInfo = document.getElementById('selected-path-info');
            if (pathInfo) {
                pathInfo.remove();
            }
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('添加路径失败:', error);
        hideLoading();
        showError('添加路径失败: ' + error.message);
    }
}

// 删除路径
async function removePath(path) {
    if (!confirm('确定要删除这个路径吗？')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('path', path);
        
        const response = await fetch('/api/remove-path', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('删除路径失败:', error);
        showError('删除路径失败: ' + error.message);
    }
}

// 重新分析照片
async function analyzePhotos() {
    try {
        showLoading('正在分析照片，请稍候...');
        
        const response = await fetch('/api/analyze', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showError(result.message);
        }
        
        hideLoading();
    } catch (error) {
        console.error('分析失败:', error);
        showError('分析失败: ' + error.message);
        hideLoading();
    }
}

// 选择文件夹（使用文件输入模拟）
function selectFolder() {
    // 创建隐藏的文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true;
    input.directory = true;
    input.multiple = true;
    
    input.onchange = function(event) {
        const files = event.target.files;
        if (files.length > 0) {
            // 获取文件的相对路径信息
            const file = files[0];
            let folderName = '';
            let suggestedPath = '';
            
            if (file.webkitRelativePath) {
                // 获取文件夹名称
                folderName = file.webkitRelativePath.split('/')[0];
                
                // 构建建议的路径（需要用户手动调整）
                suggestedPath = folderName;
            }
            
            // 显示选择结果和说明
            showFolderSelectionResult(folderName, files.length, suggestedPath);
        }
    };
    
    input.click();
}

// 显示文件夹选择结果
function showFolderSelectionResult(folderName, fileCount, suggestedPath) {
    const currentSelection = document.getElementById('current-selection');
    if (currentSelection) {
        currentSelection.innerHTML = `
            <div class="path-selection-info">
                <strong>📁 已选择文件夹：</strong> <code>${folderName}</code><br>
                <strong>📊 检测到文件：</strong> ${fileCount} 个<br>
                <br>
                <div style="background: #fff3cd; padding: 0.8rem; border-radius: 6px; border-left: 4px solid #ffc107;">
                    <strong>⚠️ 需要手动输入完整路径</strong><br>
                    <small style="color: #856404;">
                        由于浏览器安全限制，无法自动获取完整路径。<br>
                        请在上方输入框中手动输入包含"<strong>${folderName}</strong>"文件夹的完整路径。<br>
                        <br>
                        <strong>示例：</strong><br>
                        • <code>C:\\Users\\<USER>\\Pictures\\${folderName}</code><br>
                        • <code>D:\\Photos\\${folderName}</code><br>
                        • <code>C:\\${folderName}</code>（如果在C盘根目录）
                    </small>
                </div>
            </div>
        `;
    }
    
    // 在输入框中填入文件夹名作为提示
    const pathInput = document.getElementById('new-path');
    if (pathInput) {
        pathInput.placeholder = `请输入包含"${folderName}"的完整路径，例如：C:\\Photos\\${folderName}`;
        pathInput.focus();
    }
    
    // 显示操作提示
    showNotification(`已选择文件夹"${folderName}"，请在输入框中输入完整路径`, 'warning');
}

// 显示选择的路径信息
function showSelectedPath(path, fileCount) {
    // 显示在指定的当前选择区域
    const currentSelection = document.getElementById('current-selection');
    if (currentSelection) {
        currentSelection.innerHTML = `
            <div class="path-selection-info">
                <strong>📁 已选择文件夹：</strong><br>
                <code>${path}</code><br>
                <small style="color: #666;">检测到 ${fileCount} 个文件</small>
            </div>
        `;
    }
    
    // 同时移除之前的路径信息（如果存在）
    const existingInfo = document.getElementById('selected-path-info');
    if (existingInfo) {
        existingInfo.remove();
    }
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 移除之前的通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `alert alert-${type}`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.maxWidth = '400px';
    notification.innerHTML = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (notification && notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 显示加载状态
function showLoading(message = '加载中...') {
    let loadingEl = document.getElementById('loading');
    if (!loadingEl) {
        loadingEl = document.createElement('div');
        loadingEl.id = 'loading';
        loadingEl.className = 'loading';
        document.body.appendChild(loadingEl);
    }
    
    loadingEl.innerHTML = `
        <div class="spinner"></div>
        <p>${message}</p>
    `;
    loadingEl.style.display = 'block';
}

// 隐藏加载状态
function hideLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.style.display = 'none';
    }
}

// 显示成功消息
function showSuccess(message) {
    showMessage(message, 'success');
}

// 显示错误消息
function showError(message) {
    showMessage(message, 'danger');
}

// 显示消息
function showMessage(message, type) {
    // 移除现有消息
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // 创建新消息
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alert, container.firstChild);
    }
    
    // 3秒后自动移除
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

// 窗口大小改变时重新渲染图表
window.addEventListener('resize', function() {
    Object.keys(currentCharts).forEach(chartId => {
        const element = document.getElementById(chartId + '-chart');
        if (element && currentCharts[chartId]) {
            Plotly.Plots.resize(element);
        }
    });
});

// 导出功能
function exportChart(chartId, format = 'png') {
    const element = document.getElementById(chartId + '-chart');
    if (element) {
        Plotly.downloadImage(element, {
            format: format,
            width: 1200,
            height: 800,
            filename: `chart_${chartId}_${new Date().getTime()}`
        });
    }
}

// 全屏显示图表
function fullscreenChart(chartId) {
    const element = document.getElementById(chartId + '-chart');
    if (element) {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }
}

// 清空路径输入
function clearPath() {
    document.getElementById('new-path').value = '';
    // 清除路径选择信息
    const pathInfo = document.getElementById('selected-path-info');
    if (pathInfo) {
        pathInfo.remove();
    }
    // 清除当前选择显示
    const currentSelection = document.getElementById('current-selection');
    if (currentSelection) {
        currentSelection.innerHTML = '';
    }
}

// 快速路径构建助手
function showPathHelper() {
    const pathInput = document.getElementById('new-path');
    const currentValue = pathInput.value.trim();
    
    if (!currentValue) {
        showNotification('请先选择文件夹或输入文件夹名称', 'warning');
        return;
    }
    
    // 生成常见路径建议
    const suggestions = [
        `C:\\Users\\<USER>\\Pictures\\${currentValue}`,
        `C:\\Users\\<USER>\\Documents\\${currentValue}`,
        `D:\\Photos\\${currentValue}`,
        `C:\\${currentValue}`,
        `D:\\${currentValue}`
    ];
    
    // 显示路径建议
    const currentSelection = document.getElementById('current-selection');
    if (currentSelection) {
        let suggestionHtml = `
            <div class="path-selection-info">
                <strong>💡 路径建议</strong><br>
                <small style="color: #666;">点击下方路径可直接使用：</small><br><br>
        `;
        
        suggestions.forEach(path => {
            suggestionHtml += `
                <div style="margin: 0.3rem 0;">
                    <button onclick="useSuggestedPath('${path}')" 
                            style="text-align: left; width: 100%; padding: 0.5rem; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                        <code>${path}</code>
                    </button>
                </div>
            `;
        });
        
        suggestionHtml += `
                <br>
                <small style="color: #666;">💡 提示：将上述路径中的用户名部分替换为您的实际用户名</small>
            </div>
        `;
        
        currentSelection.innerHTML = suggestionHtml;
    }
}

// 使用建议的路径
function useSuggestedPath(path) {
    document.getElementById('new-path').value = path;
    showNotification('已使用建议路径，您可以根据实际情况修改', 'info');
}

// 获取当前用户名（尝试）
function getUserName() {
    // 由于安全限制，无法直接获取用户名，返回占位符
    return '用户名';
}

// 文件上传相关变量
let selectedFiles = [];

// 初始化文件上传功能
function initializeFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('photo-upload-files');
    const folderInput = document.getElementById('photo-upload-folder');
    
    if (!uploadArea) return;
    
    // 文件选择事件
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
    
    if (folderInput) {
        folderInput.addEventListener('change', handleFolderSelect);
    }
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
}

// 处理文件选择
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    addFilesToSelection(files);
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');
}

// 处理文件拖拽
function handleFileDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');
    
    const files = Array.from(event.dataTransfer.files);
    addFilesToSelection(files);
}

// 添加文件到选择列表
function addFilesToSelection(files, folderName = '') {
    const validFiles = files.filter(file => {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/tiff'];
        return validTypes.includes(file.type);
    });
    
    if (validFiles.length === 0) {
        showError('请选择有效的图片文件（JPG, PNG, TIFF）');
        return;
    }
    
    // 添加新文件，避免重复
    validFiles.forEach(file => {
        const existingFile = selectedFiles.find(f => f.name === file.name && f.size === file.size);
        if (!existingFile) {
            // 如果是从文件夹选择的，添加文件夹信息
            if (folderName && file.webkitRelativePath) {
                file.folderPath = file.webkitRelativePath;
                file.sourceFolder = folderName;
            }
            selectedFiles.push(file);
        }
    });
    
    updateFileList();
    updateUploadButton();
    
    if (folderName) {
        showNotification(`已选择文件夹"${folderName}"，找到 ${validFiles.length} 张照片`, 'success');
    } else {
        showNotification(`已选择 ${validFiles.length} 个文件`, 'success');
    }
}

// 更新文件列表显示
function updateFileList() {
    const fileListContainer = document.getElementById('selected-files');
    
    if (selectedFiles.length === 0) {
        fileListContainer.innerHTML = '';
        return;
    }
    
    let html = `
        <div class="file-list">
            <h4 style="margin-bottom: 1rem;">已选择的文件 (${selectedFiles.length})</h4>
    `;
    
    selectedFiles.forEach((file, index) => {
        const fileSize = formatFileSize(file.size);
        const folderInfo = file.sourceFolder ? `<small style="color: #666;">来自: ${file.sourceFolder}</small>` : '';
        html += `
            <div class="file-item">
                <div class="file-info">
                    <span class="file-icon">📷</span>
                    <div>
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${fileSize}</div>
                        ${folderInfo}
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span class="file-status ready">准备上传</span>
                    <button onclick="removeFile(${index})" class="btn btn-sm" style="background: none; border: none; color: #dc3545; cursor: pointer;">
                        🗑️
                    </button>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    fileListContainer.innerHTML = html;
}

// 移除文件
function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFileList();
    updateUploadButton();
    
    // 清空文件输入
    const fileInput = document.getElementById('photo-upload-files');
    const folderInput = document.getElementById('photo-upload-folder');
    if (fileInput) {
        fileInput.value = '';
    }
    if (folderInput) {
        folderInput.value = '';
    }
}

// 清空选择的文件
function clearSelectedFiles() {
    selectedFiles = [];
    updateFileList();
    updateUploadButton();
    
    // 清空文件输入
    const fileInput = document.getElementById('photo-upload-files');
    const folderInput = document.getElementById('photo-upload-folder');
    if (fileInput) {
        fileInput.value = '';
    }
    if (folderInput) {
        folderInput.value = '';
    }
    
    showNotification('已清空文件选择', 'info');
}

// 更新上传按钮状态
function updateUploadButton() {
    const uploadBtn = document.getElementById('upload-btn');
    if (uploadBtn) {
        uploadBtn.disabled = selectedFiles.length === 0;
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 上传照片
async function uploadPhotos() {
    if (selectedFiles.length === 0) {
        showError('请先选择要上传的照片');
        return;
    }
    
    const progressContainer = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const uploadBtn = document.getElementById('upload-btn');
    
    try {
        // 显示进度条
        progressContainer.style.display = 'block';
        uploadBtn.disabled = true;
        
        // 更新文件状态为上传中
        updateFileStatuses('uploading');
        
        // 统计文件夹信息
        const folderStats = {};
        let totalSize = 0;
        
        selectedFiles.forEach(file => {
            totalSize += file.size;
            if (file.sourceFolder) {
                folderStats[file.sourceFolder] = (folderStats[file.sourceFolder] || 0) + 1;
            }
        });
        
        console.log(`准备上传 ${selectedFiles.length} 个文件，总大小: ${formatFileSize(totalSize)}`);
        if (Object.keys(folderStats).length > 0) {
            console.log('文件夹统计:', folderStats);
        }
        
        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append('photos', file);
        });
        
        // 显示上传状态
        progressText.textContent = `正在上传 ${selectedFiles.length} 个文件...`;
        
        // 上传照片
        const response = await fetch('/api/upload-photos', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `上传失败: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        // 更新进度
        progressFill.style.width = '100%';
        progressText.textContent = '上传完成，正在分析...';
        
        // 更新文件状态为成功
        updateFileStatuses('success');
        
        setTimeout(() => {
            progressContainer.style.display = 'none';
            showSuccess(result.message);
            
            // 清空选择
            clearSelectedFiles();
            
            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 1500);
        }, 1000);
        
    } catch (error) {
        console.error('上传失败:', error);
        progressContainer.style.display = 'none';
        uploadBtn.disabled = false;
        updateFileStatuses('error');
        showError('上传失败: ' + error.message);
    }
}

// 更新文件状态
function updateFileStatuses(status) {
    const statusElements = document.querySelectorAll('.file-status');
    const statusText = {
        'ready': '准备上传',
        'uploading': '上传中...',
        'success': '上传成功',
        'error': '上传失败'
    };
    
    statusElements.forEach(element => {
        element.className = `file-status ${status}`;
        element.textContent = statusText[status];
    });
}

// 选择文件
function selectFiles() {
    const fileInput = document.getElementById('photo-upload-files');
    if (fileInput) {
        fileInput.click();
    }
}

// 选择文件夹
function selectPhotoFolder() {
    const folderInput = document.getElementById('photo-upload-folder');
    if (folderInput) {
        folderInput.click();
    }
}

// 处理文件夹选择
function handleFolderSelect(event) {
    const files = Array.from(event.target.files);
    
    if (files.length === 0) {
        showError('未选择任何文件');
        return;
    }
    
    // 过滤出照片文件
    const photoFiles = files.filter(file => {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/tiff'];
        return validTypes.includes(file.type);
    });
    
    if (photoFiles.length === 0) {
        showError('选择的文件夹中没有找到照片文件');
        return;
    }
    
    // 获取文件夹名称
    const folderName = files[0].webkitRelativePath.split('/')[0];
    
    showNotification(`已选择文件夹"${folderName}"，找到 ${photoFiles.length} 张照片`, 'success');
    
    // 添加到选择列表
    addFilesToSelection(photoFiles, folderName);
}

// 检查LLM状态并在UI上反映
async function checkLlmStatus() {
    try {
        const response = await fetch('/api/llm-status');
        if (!response.ok) return;
        
        const result = await response.json();
        
        // 如果LLM已启用，并且当前页面有图表，为每个图表添加分析按钮
        if (result.enabled) {
            const chartContainers = document.querySelectorAll('.chart-content');
            chartContainers.forEach(container => {
                // 检查是否已存在分析按钮
                const chartParent = container.parentNode;
                if (!chartParent.querySelector('.analysis-btn')) {
                    const chartId = container.id;
                    const chartType = chartId.replace('-chart', '').replace('-', '_');
                    
                    const analysisBtn = document.createElement('button');
                    analysisBtn.className = 'btn btn-secondary analysis-btn';
                    analysisBtn.innerHTML = '🤖 分析数据';
                    analysisBtn.onclick = () => analyzeChart(chartType);
                    
                    chartParent.insertBefore(analysisBtn, container.nextSibling);
                }
            });
        }
    } catch (error) {
        console.error('检查LLM状态失败:', error);
    }
}

// 分析图表数据
async function analyzeChart(chartType) {
    if (!currentCharts[chartType]) {
        showNotification('没有可供分析的数据', 'warning');
        return;
    }
    
    // 显示分析结果区域并显示加载动画
    const analysisContainer = document.getElementById(`${chartType}-analysis`);
    if (!analysisContainer) return;
    
    // 显示加载状态
    analysisContainer.style.display = 'block';
    analysisContainer.innerHTML = `
        <div class="ai-loading">
            <span>AI正在分析数据</span>
            <div class="ai-loading-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    try {
        // 准备请求数据
        const formData = new FormData();
        formData.append('chart_type', chartType);
        formData.append('chart_data', JSON.stringify(currentCharts[chartType]));
        
        // 发送分析请求
        const response = await fetch('/api/analyze-chart', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        // 显示分析结果
        if (result.success) {
            analysisContainer.innerHTML = `
                <h4><span>🤖 AI分析</span></h4>
                <p class="analysis-text">${result.analysis}</p>
            `;
        } else {
            analysisContainer.innerHTML = `
                <h4><span>⚠️ 分析失败</span></h4>
                <p class="analysis-text">无法获取分析结果: ${result.message}</p>
            `;
        }
    } catch (error) {
        // 显示错误信息
        analysisContainer.innerHTML = `
            <h4><span>⚠️ 错误</span></h4>
            <p class="analysis-text">分析过程中发生错误: ${error.message}</p>
        `;
    }
}

// 更新图表数据到全局变量
function updateChartData(type, data) {
    currentCharts[type] = data;
}
