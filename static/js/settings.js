/**
 * Settings Page JavaScript
 * Modern implementation for settings functionality
 */

// Page navigation functions
function showMainSettings() {
    const mainSettings = document.getElementById('main-settings');
    const settingsPages = document.querySelectorAll('.settings-page');

    if (mainSettings) {
        mainSettings.classList.remove('hidden');
    }

    settingsPages.forEach(page => {
        page.classList.add('hidden');
    });
}

function showSettingsPage(pageId) {
    const mainSettings = document.getElementById('main-settings');
    const settingsPages = document.querySelectorAll('.settings-page');
    const targetPage = document.getElementById(pageId);

    if (mainSettings) {
        mainSettings.classList.add('hidden');
    }

    settingsPages.forEach(page => {
        page.classList.add('hidden');
    });

    if (targetPage) {
        targetPage.classList.remove('hidden');
    }
}

// File path management functions
async function addPath() {
    const pathInput = document.getElementById('new-path');
    const path = pathInput.value.trim();

    if (!path) {
        if (window.photoAnalyzer) {
            window.photoAnalyzer.showToast('请输入有效路径', 'warning');
        }
        return;
    }
    
    try {
        showLoading('正在添加路径...');
        
        const formData = new FormData();
        formData.append('path', path);
        
        const response = await fetch('/api/add-path', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification(result.message, 'success');
            clearPath();
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message, 'warning');
        }
    } catch (error) {
        console.error('添加路径失败:', error);
        hideLoading();
        showNotification('添加路径失败: ' + error.message, 'danger');
    }
}

// Remove photo path
async function removePath(path) {
    if (!confirm(`确定要删除路径 "${path}" 吗？`)) {
        return;
    }
    
    try {
        showLoading('正在删除路径...');
        
        const formData = new FormData();
        formData.append('path', path);
        
        const response = await fetch('/api/remove-path', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification('路径已删除', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message || '删除路径失败', 'warning');
        }
    } catch (error) {
        console.error('删除路径失败:', error);
        hideLoading();
        showNotification('删除路径失败: ' + error.message, 'danger');
    }
}

// 清空路径输入框
function clearPath() {
    document.getElementById('new-path').value = '';
    const currentSelection = document.getElementById('current-selection');
    if (currentSelection) {
        currentSelection.innerHTML = '';
    }
}

// 浏览文件夹 (仅在支持的浏览器中有效)
function selectFolder() {
    // 创建隐藏的文件夹选择器
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true; 
    input.style.display = 'none';
    
    input.addEventListener('change', function() {
        if (this.files.length > 0) {
            const folderPath = this.files[0].path.split(this.files[0].name)[0];
            document.getElementById('new-path').value = folderPath;
            
            // 显示选择的文件夹信息
            const currentSelection = document.getElementById('current-selection');
            if (currentSelection) {
                currentSelection.innerHTML = `
                    <div class="alert alert-info">
                        <strong>已选择文件夹:</strong> ${folderPath}
                        <br><small>包含 ${this.files.length} 个文件</small>
                    </div>
                `;
            }
        }
    });
    
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
}

// 系统操作功能
// 分析照片
async function analyzePhotos() {
    try {
        showLoading('正在分析照片，请稍候...');
        
        const response = await fetch('/api/analyze', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification(result.message, 'success');
            setTimeout(() => window.location.href = '/', 1500);
        } else {
            showNotification(result.message || '分析失败', 'warning');
        }
    } catch (error) {
        console.error('分析失败:', error);
        hideLoading();
        showNotification('分析失败: ' + error.message, 'danger');
    }
}

// 清空所有路径
async function clearAllPaths() {
    if (!confirm('确定要清空所有路径吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        showLoading('正在清空路径...');
        
        const response = await fetch('/api/clear-paths', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification(result.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message || '清空失败', 'warning');
        }
    } catch (error) {
        console.error('清空路径失败:', error);
        hideLoading();
        showNotification('清空路径失败: ' + error.message, 'danger');
    }
}

// 清除缓存
async function clearCache() {
    if (!confirm('确定要清除缓存吗？这将删除所有分析数据，但保留路径配置。')) {
        return;
    }
    
    try {
        showLoading('正在清除缓存...');
        
        const response = await fetch('/api/clear-cache', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification(result.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message || '清除失败', 'warning');
        }
    } catch (error) {
        console.error('清除缓存失败:', error);
        hideLoading();
        showNotification('清除缓存失败: ' + error.message, 'danger');
    }
}

// 重置设置
async function resetSettings() {
    if (!confirm('确定要重置所有设置吗？此操作将清空所有路径和数据，且不可撤销！')) {
        return;
    }
    
    try {
        showLoading('正在重置设置...');
        
        const response = await fetch('/api/reset-settings', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification(result.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message || '重置失败', 'warning');
        }
    } catch (error) {
        console.error('重置设置失败:', error);
        hideLoading();
        showNotification('重置设置失败: ' + error.message, 'danger');
    }
}

// 刷新系统
function refreshSystem() {
    showLoading('系统刷新中...');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// 导出设置
async function exportSettings() {
    try {
        showLoading('正在准备导出...');
        
        const response = await fetch('/api/export-settings', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            // 创建下载链接
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(result.config, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "photo_analyzer_config.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            showNotification('设置已导出', 'success');
        } else {
            showNotification(result.message || '导出失败', 'warning');
        }
    } catch (error) {
        console.error('导出设置失败:', error);
        hideLoading();
        showNotification('导出设置失败: ' + error.message, 'danger');
    }
}

// 导入设置
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    
    input.onchange = async function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = async function(e) {
            try {
                const config = JSON.parse(e.target.result);
                
                showLoading('正在导入设置...');
                
                const response = await fetch('/api/import-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                hideLoading();
                
                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(result.message || '导入失败', 'warning');
                }
            } catch (error) {
                console.error('导入设置失败:', error);
                hideLoading();
                showNotification('导入设置失败: 无效的配置文件格式', 'danger');
            }
        };
        
        reader.readAsText(file);
    };
    
    input.click();
}

// LLM设置功能
async function saveLlmSettings() {
    const apiUrl = document.getElementById('llm-api-url').value.trim();
    const model = document.getElementById('llm-model').value.trim();
    const enabled = document.getElementById('llm-enabled').checked;
    
    if (!apiUrl) {
        showNotification('请输入有效的API地址', 'warning');
        return;
    }
    
    try {
        showLoading('正在保存LLM设置...');
        
        const formData = new FormData();
        formData.append('api_url', apiUrl);
        formData.append('model', model);
        formData.append('enabled', enabled);
        
        const response = await fetch('/api/save-llm-settings', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showNotification(result.message, 'success');
        } else {
            showNotification(result.message, 'warning');
        }
    } catch (error) {
        hideLoading();
        showNotification('保存设置失败: ' + error.message, 'error');
    }
}

async function testLlmConnection() {
    const apiUrl = document.getElementById('llm-api-url').value.trim();
    const model = document.getElementById('llm-model').value.trim();
    
    if (!apiUrl) {
        showNotification('请输入有效的API地址', 'warning');
        return;
    }
    
    try {
        const resultElement = document.getElementById('llm-test-result');
        resultElement.className = 'alert alert-info';
        resultElement.innerHTML = '正在测试连接，请稍候...';
        resultElement.style.display = 'block';
        
        const formData = new FormData();
        formData.append('api_url', apiUrl);
        formData.append('model', model);
        
        const response = await fetch('/api/test-llm-connection', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            resultElement.className = 'alert alert-success';
            resultElement.innerHTML = `<strong>连接成功!</strong> ${result.message}`;
        } else {
            resultElement.className = 'alert alert-warning';
            resultElement.innerHTML = `<strong>连接失败:</strong> ${result.message}`;
        }
    } catch (error) {
        const resultElement = document.getElementById('llm-test-result');
        resultElement.className = 'alert alert-error';
        resultElement.innerHTML = `<strong>请求错误:</strong> ${error.message}`;
        resultElement.style.display = 'block';
    }
}

// 内容分析相关函数
async function triggerContentAnalysis() {
    try {
        showLoading('正在启动内容分析...');
        
        const response = await fetch('/api/trigger-content-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showAnalysisStatus(result.message, 'success');
            showNotification('内容分析已启动', 'success');
        } else {
            showAnalysisStatus(result.message, 'error');
            showNotification(result.message, 'warning');
        }
    } catch (error) {
        hideLoading();
        showAnalysisStatus('启动内容分析失败: ' + error.message, 'error');
        showNotification('启动内容分析失败', 'error');
    }
}

async function checkAnalysisStatus() {
    try {
        showLoading('检查分析状态...');
        
        const response = await fetch('/api/content-analysis-status');
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            const status = result.data;
            const statusText = `总照片数: ${status.total}, 已分析: ${status.analyzed}, 待分析: ${status.unanalyzed}`;
            showAnalysisStatus(statusText, 'info');
            showNotification('状态检查完成', 'success');
        } else {
            showAnalysisStatus(result.message, 'error');
            showNotification(result.message, 'warning');
        }
    } catch (error) {
        hideLoading();
        showAnalysisStatus('检查状态失败: ' + error.message, 'error');
        showNotification('检查状态失败', 'error');
    }
}

async function generateAnalysisReport() {
    try {
        showLoading('生成分析报告...');
        
        const response = await fetch('/api/generate-analysis-report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showAnalysisStatus('分析报告生成成功', 'success');
            showNotification('分析报告生成成功', 'success');
        } else {
            showAnalysisStatus(result.message, 'error');
            showNotification(result.message, 'warning');
        }
    } catch (error) {
        hideLoading();
        showAnalysisStatus('生成报告失败: ' + error.message, 'error');
        showNotification('生成报告失败', 'error');
    }
}

function showAnalysisStatus(message, type) {
    const statusDiv = document.getElementById('content-analysis-status');
    if (statusDiv) {
        statusDiv.className = `alert alert-${type}`;
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';
        
        // 5秒后自动隐藏
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 5000);
    }
}

// UI辅助函数
function showLoading(message = '加载中...') {
    // Use the modern loading system if available
    if (window.photoAnalyzer && window.photoAnalyzer.showLoading) {
        window.photoAnalyzer.showLoading(message);
        return;
    }

    // Fallback to legacy loading system
    const existingOverlay = document.getElementById('loading-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">${message}</div>
        </div>
    `;

    document.body.appendChild(overlay);

    // 强制重排，然后添加show类
    void overlay.offsetWidth;
    overlay.classList.add('show');
}

function hideLoading() {
    // Use the modern loading system if available
    if (window.photoAnalyzer && window.photoAnalyzer.hideLoading) {
        window.photoAnalyzer.hideLoading();
        return;
    }

    // Fallback to legacy loading system
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.remove();
            }
        }, 300);
    }
}

function showNotification(message, type = 'info') {
    // Use the modern toast system if available
    if (window.photoAnalyzer && window.photoAnalyzer.showToast) {
        window.photoAnalyzer.showToast(message, type);
        return;
    }

    // Fallback to legacy notification system
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
        notification.remove();
    });
    
    // 创建新通知
    const notification = document.createElement('div');
    notification.className = `notification alert alert-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;
    notification.innerHTML = message;
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 3000);
}

// 添加CSS动画
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 123, 255, 0.2);
            border-radius: 50%;
            border-top-color: #007bff;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1rem;
            color: #555;
            font-weight: 500;
        }
    `;
    document.head.appendChild(style);

    // Initialize file upload functionality
    initFileUpload();
});

// File upload functionality
function initFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    const uploadProgress = document.getElementById('upload-progress');

    if (uploadArea && fileInput) {
        // Click to select files
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // File selection handler
        fileInput.addEventListener('change', handleFileUpload);

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('border-primary-400', 'bg-primary-50');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-primary-400', 'bg-primary-50');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-primary-400', 'bg-primary-50');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileUpload();
            }
        });
    }
}

async function handleFileUpload() {
    const fileInput = document.getElementById('file-input');
    const uploadProgress = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    if (!fileInput.files || fileInput.files.length === 0) {
        return;
    }

    const formData = new FormData();
    for (let i = 0; i < fileInput.files.length; i++) {
        formData.append('photos', fileInput.files[i]);
    }

    try {
        // Show progress
        uploadProgress.classList.remove('hidden');
        progressText.textContent = `正在上传 ${fileInput.files.length} 个文件...`;
        progressFill.style.width = '0%';

        const response = await fetch('/api/upload-photos', {
            method: 'POST',
            body: formData
        });

        // Simulate progress
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            progressFill.style.width = progress + '%';
            if (progress >= 90) {
                clearInterval(progressInterval);
            }
        }, 100);

        const result = await response.json();
        clearInterval(progressInterval);
        progressFill.style.width = '100%';

        if (result.success) {
            progressText.textContent = '上传完成！';
            showNotification(result.message || '文件上传成功', 'success');

            // Reset form after delay
            setTimeout(() => {
                uploadProgress.classList.add('hidden');
                fileInput.value = '';
                progressFill.style.width = '0%';
            }, 2000);
        } else {
            progressText.textContent = '上传失败';
            showNotification(result.message || '上传失败', 'error');
            uploadProgress.classList.add('hidden');
        }
    } catch (error) {
        console.error('Upload error:', error);
        progressText.textContent = '上传失败';
        showNotification('上传失败: ' + error.message, 'error');
        uploadProgress.classList.add('hidden');
    }
}

// Helper functions for path management
function clearPath() {
    const pathInput = document.getElementById('new-path');
    if (pathInput) {
        pathInput.value = '';
    }
    const currentSelection = document.getElementById('current-selection');
    if (currentSelection) {
        currentSelection.innerHTML = '';
    }
}

function selectFolder() {
    // This would typically open a folder dialog
    // For web applications, this is limited by browser security
    showNotification('请手动输入文件夹路径，或使用拖拽功能上传文件', 'info');
}
