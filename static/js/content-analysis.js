// 内容分析相关的JavaScript功能

// 加载内容分析图表
function loadContentAnalysisCharts() {
    // 获取分析报告数据
    fetch('/api/content-analysis-report')
        .then(response => response.json())
        .then(data => {
            if (data.report) {
                // 生成词云图
                generateWordCloud(data.report.题材分布);
                
                // 生成色调分布图
                generateToneDistribution(data.report.色调分布);
            }
        })
        .catch(error => {
            console.error('加载内容分析数据失败:', error);
        });
}

// 生成词云图
function generateWordCloud(data) {
    const wordcloudContainer = document.getElementById('subject-wordcloud');
    if (!wordcloudContainer || !data) return;
    
    // 将数据转换为词云格式
    const words = Object.entries(data).map(([word, count]) => ({
        text: word,
        size: Math.max(12, Math.min(48, count * 8)), // 根据频次调整大小
        color: getRandomColor()
    }));
    
    // 创建简单的词云布局
    let html = '<div class="wordcloud-wrapper">';
    words.forEach(word => {
        html += `<span class="word-item" style="font-size: ${word.size}px; color: ${word.color}; margin: 0.2rem;">${word.text}</span>`;
    });
    html += '</div>';
    
    wordcloudContainer.innerHTML = html;
    
    // 添加CSS样式
    if (!document.getElementById('wordcloud-styles')) {
        const style = document.createElement('style');
        style.id = 'wordcloud-styles';
        style.textContent = `
            .wordcloud-wrapper {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                align-items: center;
                height: 100%;
                padding: 1rem;
            }
            .word-item {
                font-weight: 600;
                cursor: default;
                transition: all 0.3s ease;
            }
            .word-item:hover {
                transform: scale(1.1);
                opacity: 0.8;
            }
        `;
        document.head.appendChild(style);
    }
}

// 生成色调分布图
function generateToneDistribution(data) {
    const toneChart = document.getElementById('tone-distribution');
    if (!toneChart || !data) return;
    
    const labels = Object.keys(data);
    const values = Object.values(data);
    
    const pieData = [{
        values: values,
        labels: labels,
        type: 'pie',
        hole: 0.4,
        marker: {
            colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
        },
        textinfo: 'label+percent',
        textposition: 'outside',
        font: {
            size: 12
        }
    }];
    
    const layout = {
        margin: { t: 20, r: 20, b: 20, l: 20 },
        showlegend: false,
        paper_bgcolor: 'transparent',
        plot_bgcolor: 'transparent',
        font: {
            family: 'Inter, sans-serif'
        }
    };
    
    const config = {
        responsive: true,
        displayModeBar: false
    };
    
    Plotly.newPlot('tone-distribution', pieData, layout, config);
}

// 获取随机颜色
function getRandomColor() {
    const colors = [
        '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
        '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}

// 手动触发内容分析
function triggerContentAnalysis() {
    if (confirm('确定要开始内容分析吗？这可能需要一些时间。')) {
        fetch('/api/trigger-content-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({max_analyze: 20})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('内容分析已启动，请稍后刷新页面查看结果。');
            } else {
                alert('启动分析失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('触发分析失败:', error);
            alert('启动分析失败，请稍后重试。');
        });
    }
}
