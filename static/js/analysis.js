/**
 * 照片分析器AI分析功能
 * 处理图表数据分析和显示
 */

// 存储图表数据的全局变量
let chartData = {
    focal_lengths: {},
    apertures: {},
    shutter_speeds: {},
    iso_values: {},
    devices: {},
    lenses: {}
};

// 初始化分析功能
function initAnalysis() {
    console.log("初始化AI分析功能...");
    
    // 检查LLM是否启用
    checkLlmStatus();
}

// 检查LLM状态
async function checkLlmStatus() {
    try {
        const response = await fetch('/api/llm-status');
        const result = await response.json();
        
        if (result.enabled) {
            // 如果LLM已启用，为所有图表添加分析按钮
            addAnalysisButtons();
        }
    } catch (error) {
        console.log("检查LLM状态失败:", error);
    }
}

// 为图表添加分析按钮
function addAnalysisButtons() {
    const charts = [
        { id: 'focal-lengths', type: 'focal_lengths' },
        { id: 'apertures', type: 'apertures' },
        { id: 'shutter-speeds', type: 'shutter_speeds' },
        { id: 'iso-values', type: 'iso_values' },
        { id: 'devices', type: 'devices' },
        { id: 'lenses', type: 'lenses' }
    ];
    
    charts.forEach(chart => {
        const chartContainer = document.getElementById(`${chart.id}-chart`);
        if (chartContainer) {
            const analysisBtn = document.createElement('button');
            analysisBtn.className = 'btn btn-secondary analysis-btn';
            analysisBtn.innerHTML = '🤖 分析数据';
            analysisBtn.onclick = () => analyzeChart(chart.type);
            
            chartContainer.parentNode.insertBefore(analysisBtn, chartContainer.nextSibling);
        }
    });
}

// 分析图表数据
async function analyzeChart(chartType) {
    // 检查是否已有数据
    if (!chartData[chartType] || Object.keys(chartData[chartType]).length === 0) {
        showNotification('没有可供分析的数据', 'warning');
        return;
    }
    
    // 显示分析结果区域并显示加载动画
    const analysisContainer = document.getElementById(`${chartType.replace('_', '-')}-analysis`);
    if (!analysisContainer) return;
    
    // 显示AI分析加载状态 - 带跳动圆点动画
    analysisContainer.style.display = 'block';
    analysisContainer.innerHTML = `
        <div class="ai-loading">
            <span>AI正在分析数据</span>
            <div class="ai-loading-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    try {
        // 准备请求数据
        const formData = new FormData();
        formData.append('chart_type', chartType);
        formData.append('chart_data', JSON.stringify(chartData[chartType]));
        
        // 发送分析请求
        const response = await fetch('/api/analyze-chart', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        // 显示分析结果
        if (result.success) {
            // 检查是否有结构化数据
            if (result.structured && (result.structured.habits || result.structured.suggestions)) {
                // 使用结构化数据创建更漂亮的显示
                let habitsHtml = '';
                let suggestionsHtml = '';
                
                // 处理拍摄习惯
                if (result.structured.habits && result.structured.habits.length > 0) {
                    habitsHtml = `
                        <div class="analysis-section">
                            <h5>拍摄习惯</h5>
                            <ul class="analysis-list">
                                ${result.structured.habits.map(habit => `<li>${habit}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
                
                // 处理拍摄建议
                if (result.structured.suggestions && result.structured.suggestions.length > 0) {
                    suggestionsHtml = `
                        <div class="analysis-section">
                            <h5>拍摄建议</h5>
                            <ul class="analysis-list">
                                ${result.structured.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
                
                // 组合成完整的HTML
                analysisContainer.innerHTML = `
                    <h4><span>🤖 AI分析</span></h4>
                    <div class="structured-analysis">
                        ${habitsHtml}
                        ${suggestionsHtml}
                    </div>
                `;
            } else {
                // 回退到文本格式
                analysisContainer.innerHTML = `
                    <h4><span>🤖 AI分析</span></h4>
                    <p class="analysis-text">${result.analysis}</p>
                `;
            }
        } else {
            analysisContainer.innerHTML = `
                <h4><span>⚠️ 分析失败</span></h4>
                <p class="analysis-text">无法获取分析结果: ${result.message}</p>
            `;
        }
    } catch (error) {
        // 显示错误信息
        analysisContainer.innerHTML = `
            <h4><span>⚠️ 错误</span></h4>
            <p class="analysis-text">分析过程中发生错误: ${error.message}</p>
        `;
    }
}

// 更新图表数据存储
function updateChartData(type, data) {
    chartData[type] = data;
}

// 当页面加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 给页面加载一些时间，确保图表加载完成
    setTimeout(initAnalysis, 500);
});
