/**
 * 全局Plotly配置覆盖 - 禁用图表动画但保留AI分析动画
 */
document.addEventListener('DOMContentLoaded', function() {
    // 重写全局Plotly默认配置
    if (window.Plotly) {
        // 禁用所有默认过渡效果
        Plotly.setPlotConfig({
            staticPlot: true,  // 完全禁用所有交互
            displayModeBar: false,  // 禁用模式栏
            responsive: true,  // 保持响应式
            showAxisDragHandles: false,  // 禁用轴拖动
            showAxisRangeEntryBoxes: false,  // 禁用轴范围输入框
            showTips: false,  // 禁用提示
            scrollZoom: false,  // 禁用滚动缩放
            doubleClickDelay: 1000000,  // 基本禁用双击
            displaylogo: false  // 禁用logo
        });
        
        // 覆盖Plotly动画配置
        var originalNewPlot = Plotly.newPlot;
        
        Plotly.newPlot = function() {
            var args = Array.prototype.slice.call(arguments);
            
            // 修改布局配置
            if (args.length > 2 && args[2]) {
                // 添加或覆盖过渡配置
                args[2].transition = {
                    duration: 0,  // 零持续时间
                    easing: 'linear'
                };
                
                // 禁用动画
                args[2].animate = false;
                
                // 禁用hover
                args[2].hovermode = false;
            }
            
            // 修改配置对象
            if (args.length > 3 && args[3]) {
                args[3].staticPlot = true;
                args[3].displayModeBar = false;
                args[3].responsive = true;
            } else if (args.length > 2) {
                // 如果没有提供配置对象，添加一个
                args.push({
                    staticPlot: true,
                    displayModeBar: false,
                    responsive: true
                });
            }
            
            // 调用原始函数
            return originalNewPlot.apply(this, args);
        };
    }
    
    // 禁用所有页面切换动画
    history.scrollRestoration = 'manual';
});
