/**
 * 简约界面交互脚本
 */

// 关闭照片查看器
function closePhotoViewer() {
    const viewer = document.getElementById('photoViewer');
    if (viewer) {
        viewer.style.display = 'none';
    }
}

// 加载设备和镜头统计信息
function loadDeviceStats() {
    fetch('/api/device-stats')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 更新设备统计
            document.getElementById('total-devices').textContent = data.totalDevices || '0';
            
            if (data.topDevice) {
                document.getElementById('top-device').textContent = data.topDevice.name;
            } else {
                document.getElementById('top-device').textContent = '无数据';
            }
            
            // 更新镜头统计
            document.getElementById('total-lenses').textContent = data.totalLenses || '0';
            
            if (data.topLens) {
                document.getElementById('top-lens').textContent = data.topLens.name;
            } else {
                document.getElementById('top-lens').textContent = '无数据';
            }
            
            // 更新活跃月份
            if (data.activeMonths) {
                document.getElementById('active-months').textContent = data.activeMonths;
            } else {
                document.getElementById('active-months').textContent = '无数据';
            }
        })
        .catch(error => {
            console.error('获取设备统计失败:', error);
            document.getElementById('total-devices').textContent = '加载失败';
            document.getElementById('top-device').textContent = '加载失败';
            document.getElementById('total-lenses').textContent = '加载失败';
            document.getElementById('top-lens').textContent = '加载失败';
            document.getElementById('active-months').textContent = '加载失败';
        });
}

// 分析照片
function analyzePhotos() {
    // 显示加载指示器
    showStatusMessage('正在分析照片数据，请稍候...', 'info');
    
    fetch('/api/analyze', {
        method: 'POST',
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('分析请求失败');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showStatusMessage('分析完成！刷新页面中...', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showStatusMessage('分析失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('分析请求错误:', error);
            showStatusMessage('分析请求错误，请检查控制台日志', 'error');
        });
}

// 显示状态消息
function showStatusMessage(message, type) {
    // 检查是否已存在状态消息元素
    let statusElement = document.getElementById('status-message');
    
    // 如果不存在则创建
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'status-message';
        document.body.appendChild(statusElement);
        
        // 添加样式
        statusElement.style.position = 'fixed';
        statusElement.style.bottom = '20px';
        statusElement.style.left = '50%';
        statusElement.style.transform = 'translateX(-50%)';
        statusElement.style.padding = '10px 20px';
        statusElement.style.borderRadius = '4px';
        statusElement.style.zIndex = '9999';
        statusElement.style.fontSize = '14px';
        statusElement.style.fontWeight = '500';
        statusElement.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    }
    
    // 根据类型设置样式
    switch (type) {
        case 'info':
            statusElement.style.backgroundColor = '#3498db';
            statusElement.style.color = '#fff';
            break;
        case 'success':
            statusElement.style.backgroundColor = '#2ecc71';
            statusElement.style.color = '#fff';
            break;
        case 'error':
            statusElement.style.backgroundColor = '#e74c3c';
            statusElement.style.color = '#fff';
            break;
        default:
            statusElement.style.backgroundColor = '#f8f9fa';
            statusElement.style.color = '#333';
    }
    
    // 设置消息内容
    statusElement.textContent = message;
    
    // 显示消息
    statusElement.style.display = 'block';
    
    // 自动隐藏（除非是正在处理的消息）
    if (type !== 'info') {
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 5000);
    }
}
