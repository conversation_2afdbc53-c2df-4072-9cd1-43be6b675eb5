/**
 * 图表分析功能
 * 使用异步任务处理框架来处理AI分析
 */

// 跟踪当前活动的分析任务
const activeTasks = {};

// 轮询间隔（毫秒）
const POLLING_INTERVAL = 1000;  // 初始轮询为1秒
const MAX_POLLING_INTERVAL = 5000;  // 最大轮询间隔为5秒
const POLLING_BACKOFF_FACTOR = 1.5; // 轮询回退因子

/**
 * 异步分析图表数据
 * @param {string} chartType 图表类型
 * @param {boolean} forceNew 是否强制重新分析
 */
async function asyncAnalyzeChart(chartType, forceNew = false) {
    try {
        // 如果该图表已有活动任务，则不再创建新任务
        if (chartType in activeTasks) {
            console.log(`图表 ${chartType} 已有正在进行的分析任务`);
            return;
        }

        if (!currentCharts[chartType]) {
            console.log(`跳过分析: ${chartType} 没有数据`);
            return;
        }
        
        // 显示分析结果区域并显示加载动画
        const analysisContainer = document.getElementById(`${chartType}-analysis`);
        if (!analysisContainer) {
            console.log(`找不到分析容器: ${chartType}-analysis`);
            return;
        }
        
        // 显示加载状态
        analysisContainer.style.display = 'block';
        analysisContainer.innerHTML = `
            <div class="ai-loading">
                <span>正在分析数据</span>
                <div class="ai-loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        // 准备请求数据
        const formData = new FormData();
        formData.append('chart_type', chartType);
        formData.append('chart_data', JSON.stringify({
            ...currentCharts[chartType],
            timestamp: Date.now()
        }));
        formData.append('force_new', forceNew);
        
        // 发送分析请求
        const response = await fetch('/api/analyze-chart', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        // 处理分析结果
        if (result.success) {
            // 如果已经有结果（来自缓存）
            if (result.analysis) {
                displayAnalysisResult(chartType, result.analysis, result.cached);
            } 
            // 如果是异步任务，开始轮询
            else if (result.task_id) {
                console.log(`开始轮询任务 ${result.task_id} 的状态`);
                activeTasks[chartType] = result.task_id;
                pollTaskStatus(chartType, result.task_id);
            }
        } else {
            console.log('分析请求失败:', result.message);
            analysisContainer.style.display = 'none'; // 失败时隐藏分析区域
        }
    } catch (error) {
        console.error('分析过程中发生错误:', error);
        const analysisContainer = document.getElementById(`${chartType}-analysis`);
        if (analysisContainer) {
            analysisContainer.style.display = 'none'; // 错误时隐藏分析区域
        }
    }
}

/**
 * 轮询任务状态
 * @param {string} chartType 图表类型
 * @param {string} taskId 任务ID
 * @param {number} interval 当前轮询间隔
 */
async function pollTaskStatus(chartType, taskId, interval = POLLING_INTERVAL) {
    try {
        const response = await fetch(`/api/task-status/${taskId}`);
        const result = await response.json();
        
        // 如果任务完成
        if (result.success && result.status === 'completed') {
            console.log(`任务 ${taskId} 已完成`);
            
            // 检查是否有结构化数据
            if (result.result && result.result.structured) {
                // 使用结构化数据显示分析结果
                displayAnalysisResult(chartType, result.result.structured, result.result.cached || false);
            } else if (result.result && result.result.analysis) {
                // 使用文本分析显示结果
                displayAnalysisResult(chartType, result.result.analysis, result.result.cached || false);
            } else if (result.analysis) {
                // 旧格式兼容
                displayAnalysisResult(chartType, result.analysis, false);
            }
            
            // 移除活动任务
            delete activeTasks[chartType];
            return;
        }
        
        // 如果任务失败
        if (result.status === 'failed') {
            console.error(`任务 ${taskId} 失败:`, result.message);
            
            const analysisContainer = document.getElementById(`${chartType}-analysis`);
            if (analysisContainer) {
                analysisContainer.style.display = 'none'; // 失败时隐藏分析区域
            }
            
            // 移除活动任务
            delete activeTasks[chartType];
            return;
        }
        
        // 如果任务仍在处理中，继续轮询，使用指数回退增加间隔
        console.log(`任务 ${taskId} 正在处理中，${interval}ms后再次轮询`);
        const nextInterval = Math.min(interval * POLLING_BACKOFF_FACTOR, MAX_POLLING_INTERVAL);
        
        setTimeout(() => {
            // 确保该图表的任务ID仍然是当前任务（可能已被取消）
            if (activeTasks[chartType] === taskId) {
                pollTaskStatus(chartType, taskId, nextInterval);
            }
        }, interval);
        
    } catch (error) {
        console.error(`轮询任务 ${taskId} 状态时出错:`, error);
        
        // 出错时也继续轮询，但使用较长的间隔
        setTimeout(() => {
            // 确保该图表的任务ID仍然是当前任务
            if (activeTasks[chartType] === taskId) {
                pollTaskStatus(chartType, taskId, MAX_POLLING_INTERVAL);
            }
        }, MAX_POLLING_INTERVAL);
    }
}

/**
 * 显示分析结果
 * @param {string} chartType 图表类型
 * @param {string|object} analysis 分析文本或结构化对象
 * @param {boolean} cached 是否为缓存结果
 */
function displayAnalysisResult(chartType, analysis, cached = false) {
    const analysisContainer = document.getElementById(`${chartType}-analysis`);
    if (!analysisContainer) return;
    
    // 检查是否是结构化结果
    let structuredData = null;
    let textAnalysis = analysis;
    
    if (typeof analysis === 'object' && analysis !== null) {
        structuredData = analysis;
        textAnalysis = '';
    }
    
    // 构建分析HTML
    let analysisHtml = '';
    
    if (structuredData) {
        // 使用结构化数据生成HTML
        const habits = structuredData.habits || [];
        const suggestions = structuredData.suggestions || [];
        
        // 生成拍摄习惯HTML
        if (habits && habits.length > 0) {
            analysisHtml += '<div class="analysis-section">';
            analysisHtml += '<h5>拍摄习惯分析</h5>';
            analysisHtml += '<ul class="insights-list">';
            habits.forEach(habit => {
                analysisHtml += `<li>${habit}</li>`;
            });
            analysisHtml += '</ul>';
            analysisHtml += '</div>';
        }
        
        // 生成建议HTML
        if (suggestions && suggestions.length > 0) {
            analysisHtml += '<div class="suggestions-section">';
            analysisHtml += '<h5>拍摄建议</h5>';
            analysisHtml += '<ul class="suggestions-list">';
            suggestions.forEach(suggestion => {
                analysisHtml += `<li>${suggestion}</li>`;
            });
            analysisHtml += '</ul>';
            analysisHtml += '</div>';
        }
    } else {
        // 处理文本分析，转换为HTML
        
        // 如果文本包含换行的要点列表，转换为HTML列表
        if (textAnalysis.includes('\n• ') || textAnalysis.includes('\n- ')) {
            const lines = textAnalysis.split('\n').filter(line => line.trim());
            if (lines.length > 1 && (lines[0].startsWith('• ') || lines[0].startsWith('- '))) {
                // 处理整个文本为列表
                analysisHtml = '<ul class="insights-list">' + 
                    lines.map(line => {
                        line = line.trim();
                        if (line.startsWith('• ') || line.startsWith('- ')) {
                            return '<li>' + line.substring(2).trim() + '</li>';
                        }
                        return '<li>' + line + '</li>';
                    }).join('') + 
                    '</ul>';
            } else {
                // 只有部分是列表，保留换行
                analysisHtml = '<p class="analysis-text">' + 
                    textAnalysis.replace(/\n• /g, '<br>• ').replace(/\n- /g, '<br>- ') + 
                    '</p>';
            }
        } else {
            analysisHtml = `<p class="analysis-text">${textAnalysis}</p>`;
        }
    }
    
    analysisContainer.innerHTML = `
        <h4><span>✨ 数据洞察</span></h4>
        <div class="analysis-content">
            ${analysisHtml}
        </div>
    `;
    
    // 添加刷新按钮
    const refreshButton = document.createElement('button');
    refreshButton.className = 'btn btn-sm btn-refresh';
    refreshButton.innerHTML = '<span>🔄</span> 更新分析';
    refreshButton.title = '强制重新分析当前数据';
    refreshButton.onclick = () => asyncAnalyzeChart(chartType, true);
    
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'analysis-actions';
    buttonContainer.appendChild(refreshButton);
    
    // 添加缓存指示器（如果是缓存结果）
    if (cached) {
        const cacheIndicator = document.createElement('small');
        cacheIndicator.className = 'cache-indicator';
        cacheIndicator.textContent = '📋 已加载缓存的分析结果';
        buttonContainer.appendChild(cacheIndicator);
    }
    
    analysisContainer.appendChild(buttonContainer);
}

/**
 * 加载保存的分析结果
 */
async function loadSavedAnalysisResults() {
    try {
        const response = await fetch('/api/analysis-results');
        if (!response.ok) return;
        
        const results = await response.json();
        
        // 遍历分析结果，更新UI
        for (const chartType in results) {
            const analysisContainer = document.getElementById(`${chartType}-analysis`);
            if (analysisContainer) {
                if (results[chartType].structured) {
                    // 优先使用结构化数据
                    displayAnalysisResult(chartType, results[chartType].structured, true);
                } else if (results[chartType].analysis) {
                    // 回退到文本分析
                    displayAnalysisResult(chartType, results[chartType].analysis, true);
                }
            }
        }
    } catch (error) {
        console.error('加载保存的分析结果失败:', error);
    }
}

/**
 * 检查LLM状态并自动分析图表
 */
async function checkLlmStatusAndAnalyze() {
    try {
        const response = await fetch('/api/llm-status');
        if (!response.ok) return;
        
        const result = await response.json();
        
        // 如果LLM已启用，对已加载的图表进行异步分析
        if (result.enabled) {
            // 延迟启动分析，确保图表已完全加载
            setTimeout(() => {
                // 按顺序提交分析任务，而非并行，以避免服务器负载过高
                const chartTypes = Object.keys(currentCharts);
                let index = 0;
                
                function analyzeNext() {
                    if (index < chartTypes.length) {
                        const chartType = chartTypes[index++];
                        asyncAnalyzeChart(chartType, false);
                        // 每隔一秒分析下一个图表
                        setTimeout(analyzeNext, 1000);
                    }
                }
                
                analyzeNext();
            }, 1000);
        }
    } catch (error) {
        console.error('检查LLM状态失败:', error);
    }
}

/**
 * 清除所有分析结果
 */
async function clearAllAnalysisResults() {
    try {
        if (!confirm('确定要清除所有已保存的分析结果吗？')) {
            return;
        }
        
        showLoading('正在清除分析结果...');
        
        const response = await fetch('/api/clear-analysis', {
            method: 'POST'
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            showSuccess(result.message);
            // 隐藏所有分析卡片
            document.querySelectorAll('.analysis-card').forEach(card => {
                card.style.display = 'none';
            });
        } else {
            showError(result.message);
        }
    } catch (error) {
        hideLoading();
        showError('清除分析结果失败: ' + error.message);
    }
}
